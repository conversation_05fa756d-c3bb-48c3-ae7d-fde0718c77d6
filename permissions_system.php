<?php
/**
 * نظام إدارة الصلاحيات المتقدم
 * Advanced Permissions Management System
 */

if (!class_exists('PermissionsSystem')) {
class PermissionsSystem {
    private $conn;
    private $key;
    private $current_user_id;
    private $current_user_role;
    private $user_permissions_cache = [];

    public function __construct($database_connection, $encryption_key) {
        $this->conn = $database_connection;
        $this->key = $encryption_key;
        $this->initializeCurrentUser();
    }

    /**
     * تهيئة بيانات المستخدم الحالي
     */
    private function initializeCurrentUser() {
        if (isset($_SESSION['account_id'])) {
            $this->current_user_id = decrypt($_SESSION['account_id'], $this->key);

            // جلب دور المستخدم الحالي
            $stmt = $this->conn->prepare("SELECT role FROM accounts WHERE account_id = ?");
            $stmt->bind_param("i", $this->current_user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $this->current_user_role = $row['role'];
            }
            $stmt->close();
        }
    }

    /**
     * فحص صلاحية محددة للمستخدم الحالي
     */
    public function hasPermission($module_name, $permission_name, $user_id = null) {
        $user_id = $user_id ?? $this->current_user_id;

        if (!$user_id) {
            return false;
        }

        // التحقق من الكاش أولاً
        $cache_key = "{$user_id}_{$module_name}_{$permission_name}";
        if (isset($this->user_permissions_cache[$cache_key])) {
            return $this->user_permissions_cache[$cache_key];
        }

        $has_permission = $this->checkUserPermission($user_id, $module_name, $permission_name);

        // حفظ في الكاش
        $this->user_permissions_cache[$cache_key] = $has_permission;

        return $has_permission;
    }

    /**
     * فحص الصلاحية من قاعدة البيانات
     */
    private function checkUserPermission($user_id, $module_name, $permission_name) {
        // أولاً: فحص الصلاحيات الخاصة للمستخدم
        $user_permission = $this->getUserSpecificPermission($user_id, $module_name, $permission_name);
        if ($user_permission !== null) {
            return $user_permission;
        }

        // ثانياً: فحص صلاحيات الدور
        return $this->getRolePermission($user_id, $module_name, $permission_name);
    }

    /**
     * جلب الصلاحية الخاصة للمستخدم
     */
    private function getUserSpecificPermission($user_id, $module_name, $permission_name) {
        $sql = "SELECT up.granted
                FROM user_permissions up
                JOIN modules m ON up.module_id = m.module_id
                JOIN permissions p ON up.permission_id = p.permission_id
                WHERE up.account_id = ?
                AND m.module_name = ?
                AND p.permission_name = ?
                AND (up.expires_at IS NULL OR up.expires_at > NOW())";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("iss", $user_id, $module_name, $permission_name);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $stmt->close();
            return (bool)$row['granted'];
        }

        $stmt->close();
        return null;
    }

    /**
     * جلب صلاحية الدور
     */
    private function getRolePermission($user_id, $module_name, $permission_name) {
        $sql = "SELECT rp.granted
                FROM role_permissions rp
                JOIN roles r ON rp.role_id = r.role_id
                JOIN accounts a ON a.role = r.role_name
                JOIN modules m ON rp.module_id = m.module_id
                JOIN permissions p ON rp.permission_id = p.permission_id
                WHERE a.account_id = ?
                AND m.module_name = ?
                AND p.permission_name = ?
                AND r.is_active = TRUE";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("iss", $user_id, $module_name, $permission_name);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $stmt->close();
            return (bool)$row['granted'];
        }

        $stmt->close();
        return false;
    }

    /**
     * فحص إذا كان المستخدم مدير نظام
     */
    public function isAdmin($user_id = null) {
        $user_id = $user_id ?? $this->current_user_id;

        $stmt = $this->conn->prepare("SELECT role FROM accounts WHERE account_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $stmt->close();
            return $row['role'] === 'admin';
        }

        $stmt->close();
        return false;
    }

    /**
     * جلب جميع صلاحيات المستخدم
     */
    public function getUserPermissions($user_id = null) {
        $user_id = $user_id ?? $this->current_user_id;

        $permissions = [];

        // جلب صلاحيات الدور
        $sql = "SELECT m.module_name, p.permission_name, rp.granted
                FROM role_permissions rp
                JOIN roles r ON rp.role_id = r.role_id
                JOIN accounts a ON a.role = r.role_name
                JOIN modules m ON rp.module_id = m.module_id
                JOIN permissions p ON rp.permission_id = p.permission_id
                WHERE a.account_id = ? AND r.is_active = TRUE";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $permissions[$row['module_name']][$row['permission_name']] = (bool)$row['granted'];
        }
        $stmt->close();

        // تطبيق الصلاحيات الخاصة للمستخدم (تجاوز صلاحيات الدور)
        $sql = "SELECT m.module_name, p.permission_name, up.granted
                FROM user_permissions up
                JOIN modules m ON up.module_id = m.module_id
                JOIN permissions p ON up.permission_id = p.permission_id
                WHERE up.account_id = ?
                AND (up.expires_at IS NULL OR up.expires_at > NOW())";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $permissions[$row['module_name']][$row['permission_name']] = (bool)$row['granted'];
        }
        $stmt->close();

        return $permissions;
    }

    /**
     * منح صلاحية خاصة لمستخدم
     */
    public function grantUserPermission($user_id, $module_name, $permission_name, $expires_at = null, $notes = null) {
        if (!$this->hasPermission('accounts', 'manage_permissions')) {
            throw new Exception('ليس لديك صلاحية لإدارة الصلاحيات');
        }

        // جلب معرفات الوحدة والصلاحية
        $module_id = $this->getModuleId($module_name);
        $permission_id = $this->getPermissionId($permission_name);

        if (!$module_id || !$permission_id) {
            throw new Exception('الوحدة أو الصلاحية غير موجودة');
        }

        // إدراج أو تحديث الصلاحية
        $sql = "INSERT INTO user_permissions (account_id, module_id, permission_id, granted, expires_at, created_by, notes)
                VALUES (?, ?, ?, TRUE, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                granted = TRUE, expires_at = VALUES(expires_at), notes = VALUES(notes), created_by = VALUES(created_by)";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("iiiiss", $user_id, $module_id, $permission_id, $expires_at, $this->current_user_id, $notes);
        $result = $stmt->execute();
        $stmt->close();

        if ($result) {
            $this->logPermissionChange('grant', 'user', $user_id, $module_id, $permission_id, false, true, 'منح صلاحية خاصة');
            $this->clearUserCache($user_id);
        }

        return $result;
    }

    /**
     * سحب صلاحية من مستخدم
     */
    public function revokeUserPermission($user_id, $module_name, $permission_name, $reason = null) {
        if (!$this->hasPermission('accounts', 'manage_permissions')) {
            throw new Exception('ليس لديك صلاحية لإدارة الصلاحيات');
        }

        $module_id = $this->getModuleId($module_name);
        $permission_id = $this->getPermissionId($permission_name);

        $sql = "UPDATE user_permissions
                SET granted = FALSE, created_by = ?, notes = ?
                WHERE account_id = ? AND module_id = ? AND permission_id = ?";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("isiii", $this->current_user_id, $reason, $user_id, $module_id, $permission_id);
        $result = $stmt->execute();
        $stmt->close();

        if ($result) {
            $this->logPermissionChange('revoke', 'user', $user_id, $module_id, $permission_id, true, false, $reason);
            $this->clearUserCache($user_id);
        }

        return $result;
    }

    /**
     * تسجيل تغييرات الصلاحيات
     */
    private function logPermissionChange($action, $target_type, $target_id, $module_id, $permission_id, $old_value, $new_value, $reason) {
        $sql = "INSERT INTO permission_logs (action_type, target_type, target_id, module_id, permission_id, old_value, new_value, performed_by, reason)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssiiiiiis", $action, $target_type, $target_id, $module_id, $permission_id, $old_value, $new_value, $this->current_user_id, $reason);
        $stmt->execute();
        $stmt->close();
    }

    /**
     * مساعدات للحصول على المعرفات
     */
    private function getModuleId($module_name) {
        $stmt = $this->conn->prepare("SELECT module_id FROM modules WHERE module_name = ?");
        $stmt->bind_param("s", $module_name);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();
        return $row ? $row['module_id'] : null;
    }

    private function getPermissionId($permission_name) {
        $stmt = $this->conn->prepare("SELECT permission_id FROM permissions WHERE permission_name = ?");
        $stmt->bind_param("s", $permission_name);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();
        return $row ? $row['permission_id'] : null;
    }

    /**
     * مسح كاش المستخدم
     */
    private function clearUserCache($user_id) {
        $keys_to_remove = [];
        foreach (array_keys($this->user_permissions_cache) as $key) {
            if (strpos($key, $user_id . '_') === 0) {
                $keys_to_remove[] = $key;
            }
        }
        foreach ($keys_to_remove as $key) {
            unset($this->user_permissions_cache[$key]);
        }
    }

    /**
     * فحص الوصول للصفحة
     */
    public function checkPageAccess($page_name, $required_permission = 'view') {
        if (!$this->hasPermission($page_name, $required_permission)) {
            header('HTTP/1.0 403 Forbidden');
            die('ليس لديك صلاحية للوصول لهذه الصفحة');
        }
        return true;
    }
}
}

?>
