<?php
include 'db_connection.php';
include 'encryption_functions.php';
include 'cashier_permissions.php';

// فحص صلاحية الوصول لإضافة الفواتير
requireCashierPermission('cashier_invoices', 'access', 'ليس لديك صلاحية للوصول لصفحة إضافة الفواتير');

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['account_id'])) {
    $encrypted_account_id = $_GET['account_id'];
    $account_id = decrypt($encrypted_account_id, $key);

    if ($account_id === false) {
        die("Failed to decrypt Account ID.");
    }

    // Fetch the store_id using the account_id
    $query = "SELECT store_id FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $account = $result->fetch_assoc();
    $store_id = $account['store_id'] ?? null;

    if (!$store_id) {
        die("Store ID not found for the given Account ID.");
    }

    // Encrypt the store_id
    $encrypted_store_id = encrypt($store_id, $key);

    // Fetch items related to the store
    $query = "SELECT item_id, name, price FROM items WHERE store_id = ? AND status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $items[] = $row;
    }

    // Fetch the store name using the store ID
    $query = "SELECT name FROM stores WHERE store_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $store = $result->fetch_assoc();
    $storeName = $store['name'] ?? 'اسم الفرع غير متوفر';
} else {
    die("Account ID not provided.");
}

// Fetch branches (stores except the current user's store)
$branches = [];
$query = "SELECT store_id, name FROM stores WHERE store_id != ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $branches[] = $row;
}
$stmt->close();

// Fetch accounts with role 'user'
$accounts = [];
$query = "SELECT account_id, name FROM accounts WHERE role = 'user'";
$result = $conn->query($query);
while ($row = $result->fetch_assoc()) {
    $accounts[] = $row;
}

// Define the path for the JSON file
$jsonFilePath = __DIR__ . "/saved_invoices/account_{$account_id}.json";

// Load saved items if the JSON file exists
$savedItems = [];
// Load saved invoice data if the JSON file exists
$savedInvoiceData = [];
if (file_exists($jsonFilePath)) {
    $savedInvoiceData = json_decode(file_get_contents($jsonFilePath), true) ?? [];
    $savedItems = $savedInvoiceData['items'] ?? [];
    $savedInvoiceType = $savedInvoiceData['invoice_type'] ?? 'purchase';
    $savedBranchId = $savedInvoiceData['branch_id'] ?? null;
    $savedAccountBuyerId = $savedInvoiceData['account_buyer_id'] ?? null;
} else {
    $savedInvoiceType = 'purchase';
    $savedBranchId = null;
    $savedAccountBuyerId = null;
}

?>
    <?php include 'bottom_nav.php'; ?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>إضافة فاتورة</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            text-align: center;
            background: url('https://source.unsplash.com/1600x900/?ramadan') no-repeat center center/cover;
        }

        .dark-mode {
            background-color: #1c1e21;
            color: #e4e6eb;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background-color: #007bff;
            color: #ffffff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: sticky; /* Make the header fixed */
            top: 0;
            z-index: 1000;
        }

        .search-bar {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: #f0f2f5;
            padding: 10px 0;
        }

        .dark-mode .search-bar {
            background-color: #1c1e21;
        }

        .search-bar input {
            width: 50%;
            border: none;
            outline: none;
            padding: 10px;
            font-size: 16px;
            border-radius: 25px;
            color: #007bff; /* Match the site title color */
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            padding: 12px 15px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .table th {
            background-color: #007bff !important; /* خلفية زرقاء */
            color: #ffffff; /* نص أبيض */
        }

        .dark-mode .table th {
            background-color: #0056b3 !important; /* خلفية زرقاء داكنة للوضع الليلي */
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .dark-mode .table tbody tr:hover {
            background-color: #666666;
        }

        .table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .dark-mode .table tr:nth-child(even) {
            background-color: #444444;
        }

        .table tr:hover {
            background-color: #ddd;
        }

        .dark-mode .table tr:hover {
            background-color: #666666;
        }

        .btn {
            font-family: 'Cairo', Arial, sans-serif;
        }

        .highlight {
            background-color: #007bff !important; /* خلفية زرقاء */
            color: #ffffff !important; /* نص أبيض */
        }

        .highlight td {
            background-color: #007bff !important; /* خلفية زرقاء للخلايا */
            color: #ffffff !important; /* نص أبيض للخلايا */
        }

        .highlight input {
            background-color: #ffffff; /* خلفية بيضاء لحقل الإدخال */
            color: #000000; /* نص أسود لحقل الإدخال */
        }

        .dark-mode .item-count-bar {
            background-color: #333 !important; /* خلفية داكنة */
            color: #e4e6eb !important; /* نص فاتح */
        }

        .dark-mode .item-count-bar .btn {
            background-color: #555 !important; /* زر داكن */
            color: #e4e6eb !important; /* نص فاتح للزر */
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="store-name">إضافة فاتورة - <?= htmlspecialchars($storeName) ?></div>
        <div>
            <select id="invoice-type" class="form-select" style="display: inline-block; width: auto;">
                <?php if (canCreatePurchaseInvoice()): ?>
                <option value="purchase" selected>شراء</option>
                <?php endif; ?>
                <?php if (canCreateSaleInvoice()): ?>
                <option value="sale">بيع بالجملة</option>
                <?php endif; ?>
            </select>
        </div>
    </header>
    <div id="sale-options" style="display: none; margin-top: 10px;">
        <div>
            <label for="branch-select">الفرع المشتري:</label>
            <select id="branch-select" class="form-select">
                <option value="">اختر الفرع المشتري</option>
                <?php foreach ($branches as $branch): ?>
                    <option value="<?= $branch['store_id'] ?>"><?= htmlspecialchars($branch['name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div>
            <label for="account-select">اختر الشخص المشتري:</label>
            <select id="account-select" class="form-select">
                <option value="">اختر المشتري</option>
                <?php foreach ($accounts as $account): ?>
                    <option value="<?= $account['account_id'] ?>"><?= htmlspecialchars($account['name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="search-bar">
        <input type="text" id="search-input" placeholder="ابحث عن منتج..." />
    </div>
    <div class="container mt-3">
        <div class="d-flex justify-content-between align-items-center bg-light p-3 rounded shadow-sm item-count-bar">
            <div>
                <span>عدد الأصناف المضافة: </span>
                <span id="item-count" class="fw-bold">0</span>
            </div>
            <button id="view-items" class="btn btn-primary">عرض الأصناف</button>
        </div>
    </div>
    <div class="container mt-5">
        <h2 class="text-3xl font-bold text-center text-blue-600 mb-5">قائمة المنتجات</h2>
        <div class="overflow-x-auto">
            <table class="table bg-white shadow-lg rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
                <thead class="bg-blue-500 text-white">
                    <tr>
                        <th>اسم المنتج</th>
                        <th>إضافة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($items)): ?>
                        <?php foreach ($items as $item): ?>
                            <tr data-item-id="<?= $item['item_id'] ?>" data-item-name="<?= htmlspecialchars($item['name']) ?>">
                                <td class="text-right text-gray-800 dark:text-gray-200">
                                    <i class="fas fa-box-open text-blue-500 mr-2"></i> <?= htmlspecialchars($item['name']) ?>
                                </td>
                                <td class="add-cell">
                                    <button class="btn btn-primary add-btn">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="2" class="text-center text-danger">لا توجد أصناف متاحة.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div style="margin-bottom: 80px;"></div>
    <div style="margin-bottom: 80px;"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const body = document.body;
            if (localStorage.getItem('dark-mode') === 'true') {
                body.classList.add('dark-mode');
            }

            const addedItems = <?= json_encode($savedItems) ?>;
            const itemCountElement = document.getElementById('item-count');
            const viewItemsButton = document.getElementById('view-items');
            const invoiceType = document.getElementById('invoice-type');
            const branchSelect = document.getElementById('branch-select');
            const buyerSelect = document.getElementById('account-select');
            const saleOptions = document.getElementById('sale-options');
            let capturedImages = []; // Store image files

            // Restore saved items
            function restoreSavedItems() {
                addedItems.forEach(item => {
                    const row = document.querySelector(`tr[data-item-id="${item.id}"]`);
                    if (row) {
                        const nameCell = row.querySelector('td:first-child');
                        const cell = row.querySelector('.add-cell');

                        row.classList.add('highlight');
                        nameCell.innerHTML = `
                            <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                            ${item.name}
                        `;
                        cell.innerHTML = `
                            <input type="number" class="form-control quantity-input" placeholder="الكمية" min="1" value="${item.quantity}" style="width: 80px; margin: auto;">
                        `;

                        // Attach event listeners for quantity change and item removal
                        attachRowEvents(row, item);
                    }
                });

                // Update item count after restoring items
                itemCountElement.textContent = addedItems.length;
            }

            // Attach event listeners to a row
            function attachRowEvents(row, item) {
                const nameCell = row.querySelector('td:first-child');
                const cell = row.querySelector('.add-cell');

                // Handle quantity change
                const quantityInput = cell.querySelector('.quantity-input');
                quantityInput.addEventListener('input', function () {
                    const savedItem = addedItems.find(savedItem => savedItem.id === item.id);
                    if (savedItem) {
                        savedItem.quantity = parseInt(this.value) || 1;
                        saveItemsToFile();
                    }
                });

                // Handle item removal
                const removeIcon = nameCell.querySelector('.remove-item');
                removeIcon.addEventListener('click', function () {
                    const index = addedItems.findIndex(savedItem => savedItem.id === item.id);
                    if (index !== -1) {
                        addedItems.splice(index, 1);
                        saveItemsToFile();
                    }

                    row.classList.remove('highlight');
                    nameCell.innerHTML = `
                        <i class="fas fa-box-open text-blue-500 mr-2"></i> ${item.name}
                    `;
                    cell.innerHTML = `
                        <button class="btn btn-primary add-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    `;

                    // Update item count
                    itemCountElement.textContent = addedItems.length;

                    // Reattach the click event to the "+" button
                    cell.querySelector('.add-btn').addEventListener('click', function () {
                        addItem(row, item.id, item.name);
                    });
                });
            }

            // Add an item to the list
            function addItem(row, itemId, itemName) {
                // Prevent adding items if invoice type is "sale" and branch or buyer is not selected
                if (invoiceType.value === 'sale') {
                    if (!branchSelect.value || !buyerSelect.value) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'يرجى اختيار الفرع المشتري والشخص المشتري',
                            text: 'لا يمكنك إضافة أصناف إلى الفاتورة قبل تحديد الفرع المشتري والشخص المشتري.',
                            confirmButtonText: 'حسنًا'
                        });
                        return;
                    }
                }

                // Check if the item is already added
                if (addedItems.some(item => item.id === itemId)) {
                    Swal.fire('تنبيه', 'هذا الصنف مضاف بالفعل.', 'info');
                    return;
                }

                // Add the item
                addedItems.push({ id: itemId, name: itemName, quantity: 1 });
                row.classList.add('highlight');
                const nameCell = row.querySelector('td:first-child');
                const cell = row.querySelector('.add-cell');
                nameCell.innerHTML = `
                    <i class="fas fa-trash text-danger remove-item" style="cursor: pointer; margin-right: 10px;"></i>
                    ${itemName}
                `;
                cell.innerHTML = `
                    <input type="number" class="form-control quantity-input" placeholder="الكمية" min="1" value="1" style="width: 80px; margin: auto;">
                `;
                itemCountElement.textContent = addedItems.length;
                saveItemsToFile();

                // Attach events for the new row
                attachRowEvents(row, { id: itemId, name: itemName, quantity: 1 });
            }

            // Save items to the JSON file
            function saveItemsToFile() {
                const encryptedAccountId = <?= json_encode($encrypted_account_id) ?>;
                const params = new URLSearchParams();
                params.append('account_id', encryptedAccountId);
                params.append('items', JSON.stringify(addedItems));
                params.append('invoice_type', invoiceType.value);

                // Include branch and buyer only for "sale" invoices
                if (invoiceType.value === 'sale') {
                    params.append('branch_id', branchSelect.value);
                    params.append('account_buyer_id', buyerSelect.value);
                }

                const url = `save_invoice.php?${params.toString()}`;
                fetch(url, { method: 'GET' });
            }

            // Restore saved items on page load
            restoreSavedItems();

            // Handle adding items
            document.querySelectorAll('.add-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const row = this.closest('tr');
                    const itemId = row.getAttribute('data-item-id');
                    const itemName = row.getAttribute('data-item-name');
                    addItem(row, itemId, itemName);
                });
            });

            // Create FormData for submission
            function createFormData(data) {
                const formData = new FormData();
                formData.append('store_id', data.store_id);
                formData.append('account_id', data.account_id);
                if (data.branch_id) formData.append('branch_id', data.branch_id);
                if (data.account_buyer_id) formData.append('account_buyer_id', data.account_buyer_id);
                formData.append('items', JSON.stringify(data.items));
                data.images.forEach((path, i) => formData.append(`images[${i}]`, path));
                return formData;
            }

            // Compress image before upload
            async function compressImage(file) {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => {
                        const maxDim = 800;
                        let { width, height } = img;
                        if (width > height && width > maxDim) {
                            height = height * (maxDim / width);
                            width = maxDim;
                        } else if (height > maxDim) {
                            width = width * (maxDim / height);
                            height = maxDim;
                        }
                        const canvas = document.createElement('canvas');
                        canvas.width = width;
                        canvas.height = height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);
                        canvas.toBlob(blob => {
                            resolve(new File([blob], file.name, { type: 'image/jpeg' }));
                        }, 'image/jpeg', 0.75); // Compress to 75% quality
                    };
                    img.src = URL.createObjectURL(file);
                });
            }

            // Upload images in parallel
            async function uploadImagesInParallel(images, type) {
                const compressedImages = await Promise.all(images.map(file => compressImage(file)));
                const uploadPromises = compressedImages.map((file) => {
                    const formData = new FormData();
                    formData.append('image', file);
                    formData.append('type', type); // Include invoice type
                    return fetch('upload_image.php', {
                        method: 'POST',
                        body: formData
                    }).then(res => res.json());
                });

                return Promise.all(uploadPromises);
            }

            // Handle viewing items
            viewItemsButton.addEventListener('click', async function () {
                if (addedItems.length === 0) {
                    Swal.fire('لا توجد أصناف مضافة', 'يرجى إضافة أصناف أولاً.', 'info');
                    return;
                }

                let tableContent = `
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>اسم المنتج</th>
                                <th>الكمية</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                addedItems.forEach(item => {
                    tableContent += `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.quantity}</td>
                        </tr>
                    `;
                });
                tableContent += `
                        </tbody>
                    </table>
                `;

                Swal.fire({
                    title: 'الأصناف المضافة',
                    html: `
                        ${tableContent}
                        <button id="upload-file" class="btn btn-secondary mt-3">
                            <i class="fas fa-upload"></i> تحميل ملف
                        </button>
                        <div id="image-gallery" style="margin-top: 20px; display: flex; flex-wrap: wrap; gap: 10px;"></div>
                        <input type="file" id="file-input" accept="image/*" multiple style="display: none;">
                    `,
                    width: '600px',
                    showCancelButton: true,
                    confirmButtonText: 'تأكيد',
                    cancelButtonText: 'استكمال الاصناف',
                    didOpen: () => {
                        const uploadFileButton = Swal.getPopup().querySelector('#upload-file');
                        const fileInput = Swal.getPopup().querySelector('#file-input');
                        const imageGallery = Swal.getPopup().querySelector('#image-gallery');

                        uploadFileButton.addEventListener('click', () => fileInput.click());
                        fileInput.addEventListener('change', (event) => {
                            const files = event.target.files;
                            Array.from(files).forEach(file => {
                                capturedImages.push(file);
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                    const thumbnail = document.createElement('div');
                                    thumbnail.style.position = 'relative';
                                    thumbnail.style.width = '80px';
                                    thumbnail.style.height = '80px';
                                    thumbnail.style.border = '1px solid #ddd';
                                    thumbnail.style.borderRadius = '5px';
                                    thumbnail.style.overflow = 'hidden';
                                    thumbnail.innerHTML = `
                                        <img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover;">
                                        <i class="fas fa-trash text-danger" style="position: absolute; top: 5px; right: 5px; cursor: pointer; background: white; border-radius: 50%; padding: 5px;"></i>
                                    `;
                                    thumbnail.querySelector('.fa-trash').addEventListener('click', () => {
                                        capturedImages = capturedImages.filter(img => img !== file);
                                        thumbnail.remove();
                                    });
                                    imageGallery.appendChild(thumbnail);
                                };
                                reader.readAsDataURL(file);
                            });
                        });
                    },
                    preConfirm: async () => {
                        if (capturedImages.length === 0) {
                            Swal.showValidationMessage('يجب تحميل صورة واحدة على الأقل.');
                            return false;
                        }

                        const invoiceTypeValue = invoiceType.value;
                        const uploadedImagePaths = await uploadImagesInParallel(capturedImages, invoiceTypeValue);
                        const branchId = invoiceTypeValue === 'sale' ? branchSelect.value : null;
                        const accountBuyerId = invoiceTypeValue === 'sale' ? buyerSelect.value : null;

                        return fetch(invoiceTypeValue === 'sale' ? 'confirm_sale_invoice.php' : 'confirm_invoice.php', {
                            method: 'POST',
                            body: createFormData({
                                store_id: <?= json_encode($store_id) ?>,
                                account_id: <?= json_encode($account_id) ?>,
                                branch_id: branchId,
                                account_buyer_id: accountBuyerId,
                                items: addedItems,
                                images: uploadedImagePaths.map(res => res.path)
                            })
                        }).then(res => res.json());
                    }
                }).then(result => {
                    if (result.isConfirmed) {
                        Swal.fire('تم الحفظ', 'تم حفظ الفاتورة بنجاح.', 'success').then(() => location.reload());
                    }
                });
            });

            // Toggle sale options visibility based on invoice type
            invoiceType.addEventListener('change', function () {
                if (this.value === 'sale') {
                    saleOptions.style.display = 'block';
                } else {
                    saleOptions.style.display = 'none';
                }
                saveItemsToFile();
            });

            branchSelect.addEventListener('change', saveItemsToFile);
            buyerSelect.addEventListener('change', saveItemsToFile);

            // Ensure correct visibility on page load
            if (invoiceType.value === 'sale') {
                saleOptions.style.display = 'block';
            } else {
                saleOptions.style.display = 'none';
            }

            // Restore saved invoice type, branch, and buyer
            invoiceType.value = <?= json_encode($savedInvoiceType) ?>;
            saleOptions.style.display = invoiceType.value === 'sale' ? 'block' : 'none';
            branchSelect.value = <?= json_encode($savedBranchId) ?>;
            buyerSelect.value = <?= json_encode($savedAccountBuyerId) ?>;

            // Toggle sale options visibility based on restored invoice type
            if (invoiceType.value === 'sale') {
                document.getElementById('sale-options').style.display = 'block';
            }

            // Handle search functionality
            const searchInput = document.getElementById('search-input');
            searchInput.addEventListener('input', function () {
                const query = this.value.toLowerCase();
                const rows = document.querySelectorAll('table tbody tr');

                rows.forEach(row => {
                    const itemName = row.getAttribute('data-item-name').toLowerCase();
                    if (itemName.includes(query)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
    </script>

</body>
</html>
