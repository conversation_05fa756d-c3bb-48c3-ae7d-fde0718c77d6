<?php
/**
 * اختبار نظام الصلاحيات المنظم
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين الملفات المطلوبة
include_once 'db_connection.php';
include_once 'encryption_functions.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// محاكاة تسجيل دخول مدير
if (!isset($_SESSION['account_id'])) {
    $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
    $admin_result = $conn->query($admin_query);
    
    if ($admin_result && $admin_result->num_rows > 0) {
        $admin = $admin_result->fetch_assoc();
        $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
        $_SESSION['username'] = 'admin_test';
        $_SESSION['role'] = 'admin';
    }
}

// تضمين نظام الصلاحيات
include_once 'permissions_system.php';
include_once 'auth_check.php';

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصلاحيات المنظم</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .permission-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .permission-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .permission-card.success {
            border-right-color: #27ae60;
            background: linear-gradient(135deg, #d5f4e6 0%, #e8f8f0 100%);
        }
        
        .permission-card.error {
            border-right-color: #e74c3c;
            background: linear-gradient(135deg, #fdeaea 0%, #fdf2f2 100%);
        }
        
        .role-section {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        
        .role-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: bold;
        }
        
        .status-yes {
            color: #27ae60;
            font-weight: bold;
            font-size: 18px;
        }
        
        .status-no {
            color: #e74c3c;
            font-weight: bold;
            font-size: 18px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .links-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .test-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
            <i class="fas fa-shield-alt"></i> نظام الصلاحيات المنظم
        </h1>
        
        <!-- إحصائيات النظام -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-chart-pie"></i> إحصائيات النظام
            </div>
            
            <?php
            try {
                $stats = [
                    'الوحدات' => $conn->query("SELECT COUNT(*) as count FROM modules")->fetch_assoc()['count'],
                    'الصلاحيات' => $conn->query("SELECT COUNT(*) as count FROM permissions")->fetch_assoc()['count'],
                    'الأدوار' => $conn->query("SELECT COUNT(*) as count FROM roles")->fetch_assoc()['count'],
                    'صلاحيات الأدوار' => $conn->query("SELECT COUNT(*) as count FROM role_permissions")->fetch_assoc()['count']
                ];
                
                echo "<div class='stats-grid'>";
                foreach ($stats as $item => $count) {
                    echo "<div class='stat-card'>";
                    echo "<div class='stat-number'>$count</div>";
                    echo "<div>$item</div>";
                    echo "</div>";
                }
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- الصلاحيات الجديدة -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-key"></i> الصلاحيات المنطقية الجديدة
            </div>
            
            <?php
            $new_permissions = [
                'confirm_invoice' => 'تأكيد الفاتورة',
                'view_invoice_details' => 'عرض تفاصيل الفاتورة',
                'upload_images' => 'رفع الصور',
                'generate_reports' => 'إنشاء التقارير',
                'search_data' => 'البحث في البيانات',
                'change_status' => 'تغيير الحالة',
                'access_temp_data' => 'الوصول للبيانات المؤقتة'
            ];
            
            echo "<div class='permission-grid'>";
            foreach ($new_permissions as $permission => $description) {
                $permission_exists = $conn->query("SELECT COUNT(*) as count FROM permissions WHERE permission_name = '$permission'")->fetch_assoc()['count'];
                $class = $permission_exists > 0 ? 'success' : 'error';
                $icon = $permission_exists > 0 ? 'check-circle' : 'times-circle';
                
                echo "<div class='permission-card $class'>";
                echo "<i class='fas fa-$icon'></i> $description";
                echo "<br><small style='color: #666;'>$permission</small>";
                echo "</div>";
            }
            echo "</div>";
            ?>
        </div>
        
        <!-- اختبار صلاحيات الأدوار -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-users-cog"></i> صلاحيات الأدوار المنطقية
            </div>
            
            <?php
            $roles_to_test = [
                1 => ['name' => 'مدير النظام', 'color' => '#e74c3c'],
                2 => ['name' => 'مدير فرع', 'color' => '#3498db'],
                3 => ['name' => 'مشتري', 'color' => '#27ae60'],
                4 => ['name' => 'مستخدم عادي', 'color' => '#f39c12'],
                6 => ['name' => 'محاسب', 'color' => '#9b59b6']
            ];
            
            $modules_to_test = [
                'purchases' => 'فواتير الشراء',
                'sales' => 'فواتير البيع',
                'items' => 'إدارة الأصناف',
                'reports' => 'التقارير'
            ];
            
            $permissions_to_test = [
                'view' => 'عرض',
                'create' => 'إنشاء',
                'edit' => 'تعديل',
                'delete' => 'حذف',
                'export' => 'تصدير',
                'generate_reports' => 'التقارير'
            ];
            
            foreach ($roles_to_test as $role_id => $role_info) {
                echo "<div class='role-section'>";
                echo "<div class='role-header' style='background: {$role_info['color']};'>";
                echo "<i class='fas fa-user-tag'></i> {$role_info['name']}";
                echo "</div>";
                
                echo "<table>";
                echo "<tr><th>الوحدة</th>";
                foreach ($permissions_to_test as $perm_key => $perm_name) {
                    echo "<th>$perm_name</th>";
                }
                echo "</tr>";
                
                foreach ($modules_to_test as $module => $module_desc) {
                    echo "<tr><td><strong>$module_desc</strong></td>";
                    
                    foreach ($permissions_to_test as $perm_key => $perm_name) {
                        $sql = "SELECT COUNT(*) as count FROM role_permissions rp 
                                JOIN modules m ON rp.module_id = m.module_id 
                                JOIN permissions p ON rp.permission_id = p.permission_id 
                                WHERE rp.role_id = $role_id 
                                AND m.module_name = '$module' 
                                AND p.permission_name = '$perm_key' 
                                AND rp.granted = TRUE";
                        
                        $result = $conn->query($sql);
                        $has_permission = $result->fetch_assoc()['count'] > 0;
                        
                        $status_class = $has_permission ? 'status-yes' : 'status-no';
                        $status_text = $has_permission ? '✓' : '✗';
                        
                        echo "<td class='$status_class'>$status_text</td>";
                    }
                    echo "</tr>";
                }
                
                echo "</table>";
                echo "</div>";
            }
            ?>
        </div>
        
        <!-- اختبار دوال النظام -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-code"></i> اختبار دوال النظام
            </div>
            
            <?php
            try {
                echo "<div class='permission-grid'>";
                
                // اختبار دالة hasAnyPermission
                $test_permissions = [
                    ['purchases', ['view'], 'عرض فواتير الشراء'],
                    ['purchases', ['create'], 'إنشاء فواتير الشراء'],
                    ['purchases', ['edit'], 'تعديل فواتير الشراء'],
                    ['purchases', ['delete'], 'حذف فواتير الشراء'],
                    ['purchases', ['export'], 'تصدير فواتير الشراء'],
                    ['purchases', ['generate_reports'], 'تقارير فواتير الشراء']
                ];
                
                foreach ($test_permissions as $test) {
                    $has_perm = hasAnyPermission($test[0], $test[1]);
                    $class = $has_perm ? 'success' : 'error';
                    $icon = $has_perm ? 'check-circle' : 'times-circle';
                    
                    echo "<div class='permission-card $class'>";
                    echo "<i class='fas fa-$icon'></i> {$test[2]}";
                    echo "<br><small style='color: #666;'>النتيجة: " . ($has_perm ? 'مسموح' : 'غير مسموح') . "</small>";
                    echo "</div>";
                }
                
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>خطأ في اختبار الدوال: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-link"></i> روابط الاختبار
            </div>
            <div class="links-section">
                <a href="purchase_invoices.php?store_id=<?php echo urlencode(encrypt(1, $key)); ?>" class="test-link">
                    <i class="fas fa-shopping-cart"></i> فواتير الشراء
                </a>
                <a href="add_purchase_invoice.php?store_id=<?php echo urlencode(encrypt(1, $key)); ?>" class="test-link">
                    <i class="fas fa-plus"></i> إضافة فاتورة
                </a>
                <a href="manage_permissions.php" class="test-link">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                </a>
                <a href="test_permissions.php" class="test-link">
                    <i class="fas fa-vial"></i> اختبار عام
                </a>
                <a href="stores.php" class="test-link">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>
