<style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');
    /* Enhanced Sidebar Style */
    #sidebar.styled-sidebar {
        background: linear-gradient(145deg, #ffffff, #f8f9ff);
        width: 300px;
        position: fixed;
        right: 0;
        top: 0; /* Reset top position */
        height: calc(100% - 60px); /* Adjust height to account for the footer bar */
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #3f51b5 #f5f7fa;
        transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1);
        transform: translateX(100%);
        z-index: 1300; /* updated to be above the header (header has z-index: 1200) */
        padding: 25px 20px;
        box-shadow: -8px 0 30px rgba(0, 0, 0, 0.15);
        border-radius: 24px 0 0 24px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    #sidebar.styled-sidebar::-webkit-scrollbar {
        width: 6px;
    }

    #sidebar.styled-sidebar::-webkit-scrollbar-thumb {
        background-color: #3f51b5;
        border-radius: 10px;
        border: 2px solid transparent;
        background-clip: padding-box;
    }

    #sidebar.styled-sidebar::-webkit-scrollbar-track {
        background-color: rgba(245, 247, 250, 0.5);
        border-radius: 10px;
    }

    #sidebar.styled-sidebar::-webkit-scrollbar-thumb:hover {
        background-color: #303f9f;
    }

    #sidebar.open {
        transform: translateX(0);
    }

    /* Modern Toggle Icon */
    .menu-icon {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #3f51b5, #1abc9c);
        color: white;
        border-radius: 50%;
        padding: 15px;
        cursor: pointer;
        z-index: 1100;
        box-shadow: 0 4px 20px rgba(63, 81, 181, 0.4);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-size: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 50px;
        height: 50px;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .menu-icon:hover {
        background: linear-gradient(135deg, #1abc9c, #3f51b5);
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 8px 30px rgba(63, 81, 181, 0.6);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .menu-icon i {
        font-size: 22px;
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* إضافة أنيميشن البَلس للأيقونة */
    .menu-icon i.bounce {
        animation: pulse 0.5s;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .menu-icon.open i {
        transform: rotate(180deg); /* Rotate icon when sidebar is open */
    }

    #sidebar ul {
        list-style: none;
        padding: 0;
        margin-top: 30px;
    }

    #sidebar ul li {
        margin-bottom: 8px;
        position: relative;
    }

    #sidebar ul li a,
    #sidebar ul li span {
        display: flex;
        align-items: center;
        color: #333;
        background-color: transparent;
        padding: 12px 18px;
        text-decoration: none;
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        overflow: hidden;
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    #sidebar ul li a::before,
    #sidebar ul li span::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 3px;
        height: 100%;
        background-color: #3f51b5;
        transform: scaleY(0);
        transition: transform 0.3s ease;
        border-radius: 0 3px 3px 0;
    }

    #sidebar ul li a i,
    #sidebar ul li span i {
        margin-left: 12px;
        font-size: 18px;
        transition: all 0.3s ease;
        width: 24px;
        text-align: center;
    }

    #sidebar ul li a:hover,
    #sidebar ul li span:hover {
        background-color: rgba(63, 81, 181, 0.08);
        transform: translateX(-5px);
        padding-right: 23px;
    }

    #sidebar ul li a:hover::before,
    #sidebar ul li span:hover::before {
        transform: scaleY(1);
    }

    #sidebar ul li a:hover i,
    #sidebar ul li span:hover i {
        transform: scale(1.2);
        color: #3f51b5;
    }

    #sidebar ul li a.active {
        background: linear-gradient(145deg, #3f51b5, #5c6bc0);
        color: white;
        font-weight: 600;
        box-shadow: 0 6px 15px rgba(63, 81, 181, 0.4);
        position: relative;
        overflow: hidden;
        transform: translateX(-5px);
        padding-right: 23px;
        border-radius: 12px 0 0 12px;
        border-right: 4px solid #ffeb3b;
        animation: activeBorder 2s ease-in-out infinite;
    }

    @keyframes activeBorder {
        0% { border-right-color: #ffeb3b; }
        50% { border-right-color: #ff9800; }
        100% { border-right-color: #ffeb3b; }
    }

    #sidebar ul li a.active::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        background-color: #ffeb3b;
        transform: scaleY(1);
    }

    #sidebar ul li a.active::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
        transform: translateX(-100%);
        animation: activeLink 3s ease-in-out infinite;
    }

    @keyframes activeLink {
        0% { transform: translateX(-100%); }
        50% { transform: translateX(100%); }
        100% { transform: translateX(-100%); }
    }

    #sidebar ul li a.active i {
        color: white;
        animation: activeIcon 2s ease-in-out infinite;
        transform-origin: center;
        margin-left: 15px;
        font-size: 1.2em;
        text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        position: relative;
        z-index: 2;
    }

    #sidebar ul li a.active i::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        background: radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        z-index: -1;
        animation: iconGlow 2s ease-in-out infinite;
    }

    @keyframes iconGlow {
        0% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
        50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.2); }
        100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
    }

    @keyframes activeIcon {
        0% { transform: scale(1) rotate(0deg); text-shadow: 0 0 0 rgba(255,255,255,0); }
        25% { transform: scale(1.2) rotate(5deg); text-shadow: 0 0 15px rgba(255,255,255,0.7); }
        50% { transform: scale(1.1) rotate(0deg); text-shadow: 0 0 10px rgba(255,255,255,0.5); }
        75% { transform: scale(1.15) rotate(-5deg); text-shadow: 0 0 15px rgba(255,255,255,0.7); }
        100% { transform: scale(1) rotate(0deg); text-shadow: 0 0 0 rgba(255,255,255,0); }
    }

    /* Submenu */
    #sidebar ul li ul {
        padding-right: 15px;
        margin-top: 5px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s ease-in-out;
    }

    #sidebar ul li:hover ul {
        max-height: 500px; /* Large enough to contain all submenu items */
    }

    /* تنسيقات عنصر القائمة مع السهم */
    .menu-item-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
    }

    .menu-item-container a {
        flex: 1;
    }

    .submenu-toggle {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        transition: all 0.3s ease;
        margin-right: 5px;
    }

    .submenu-toggle:hover {
        background-color: rgba(63, 81, 181, 0.1);
    }

    .submenu-toggle i {
        font-size: 12px;
        transition: transform 0.3s ease;
    }

    .submenu-toggle.open i {
        transform: rotate(180deg);
    }

    /* تنسيقات قائمة التصنيفات الفرعية */
    .categories-submenu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s ease-in-out;
        margin-top: 5px;
        padding-right: 15px;
    }

    .categories-submenu.open {
        max-height: 300px !important;
        overflow-y: auto !important;
        display: block !important;
        opacity: 1 !important;
        border-right: 2px solid rgba(63, 81, 181, 0.3);
        margin-top: 8px !important;
        padding-top: 5px !important;
        padding-bottom: 5px !important;
    }

    /* تنسيق scrollbar للقائمة الفرعية */
    .categories-submenu::-webkit-scrollbar {
        width: 4px;
    }

    .categories-submenu::-webkit-scrollbar-track {
        background: transparent;
    }

    .categories-submenu::-webkit-scrollbar-thumb {
        background-color: rgba(63, 81, 181, 0.5);
        border-radius: 4px;
    }

    .loading-item span {
        display: flex;
        align-items: center;
        padding: 8px 10px;
        color: #666;
        font-size: 0.9rem;
    }

    .loading-item i {
        margin-left: 8px;
    }

    /* Always show accounts submenu - targeting the accounts menu specifically */
    #sidebar ul li:nth-child(9) ul {
        max-height: 500px !important;
        overflow: visible !important;
        display: block !important;
        opacity: 1 !important;
    }

    #sidebar ul li ul li {
        margin-bottom: 5px;
        opacity: 0.9;
        transform: translateX(5px);
        transition: all 0.3s ease;
    }

    #sidebar ul li:hover ul li {
        transform: translateX(0);
    }

    #sidebar ul li ul li a {
        font-size: 0.9rem;
        background-color: rgba(63, 81, 181, 0.05);
        padding: 10px 15px;
        border-radius: 8px;
        position: relative;
    }

    #sidebar ul li ul li a.subcategory-link {
        background-color: rgba(63, 81, 181, 0.08);
        border-right: 2px solid #3f51b5;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #sidebar ul li ul li a.subcategory-link:hover {
        background-color: rgba(63, 81, 181, 0.12);
        transform: translateX(-3px);
    }

    #sidebar ul li ul li a::before {
        width: 2px;
    }

    #sidebar ul li span {
        cursor: pointer;
    }

    /* Logout Button */
    #sidebar ul li.logout {
        margin-top: 20px;
    }

    #sidebar ul li.logout a {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        text-align: center;
        color: #fff;
        font-weight: bold;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
        position: relative;
        overflow: hidden;
    }

    #sidebar ul li.logout a::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    #sidebar ul li.logout a:hover {
        background: linear-gradient(135deg, #c0392b, #e74c3c);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
    }

    #sidebar ul li.logout a:hover::after {
        opacity: 1;
    }

    #sidebar ul li.logout a:hover i {
        animation: logoutIconPulse 0.6s infinite alternate;
        color: white;
    }

    @keyframes logoutIconPulse {
        from { transform: scale(1) rotate(0); }
        to { transform: scale(1.2) rotate(-10deg); }
    }

    /* Store Name Container */
    .store-name-container {
        text-align: center;
        margin-bottom: 25px;
        padding: 18px 15px;
        background: linear-gradient(135deg, #3f51b5, #5c6bc0);
        border-radius: 16px;
        color: white;
        box-shadow: 0 6px 15px rgba(63, 81, 181, 0.3);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .store-name-container::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    .store-name-container:hover::before {
        opacity: 1;
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .store-name {
        font-size: 1.6em;
        font-weight: bold;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .store-name i {
        margin-left: 10px;
        font-size: 1.2em;
        animation: storeIconFloat 3s ease-in-out infinite;
    }

    @keyframes storeIconFloat {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
    }

    /* Store Status Indicator */
    #store-status {
        position: fixed !important; /* Use !important to ensure it stays fixed */
        top: 140px !important; /* Positioned right below the header */
        right: 15px !important;
        padding: 10px 16px;
        border-radius: 12px;
        font-size: 14px;
        z-index: 1000; /* Lower z-index to not appear above sidebar */
        display: flex;
        align-items: center;
        gap: 8px;
        background-color: var(--color-secondary, #ffffff);
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        color: var(--color-fg, #333333);
        font-family: 'Cairo', sans-serif;
        font-weight: 500;
        opacity: 1;
        cursor: pointer;
        width: auto;
        max-width: 150px; /* Limit the width */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border-left: 3px solid var(--color-primary, #3f51b5);
        position: relative;
        overflow: hidden;
    }

    #store-status::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    #store-status:hover::before {
        opacity: 1;
        animation: shimmer 2s infinite;
    }

    #store-status:hover {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    /* Stores Dropdown */
    #stores-dropdown {
        position: fixed !important; /* Use !important to ensure it stays fixed */
        top: 180px !important; /* Position below the store status indicator */
        right: 15px !important; /* Same right position as store status */
        width: 180px;
        max-height: 300px;
        overflow-y: auto;
        background-color: var(--color-secondary, #ffffff);
        border: none;
        border-radius: 12px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        z-index: 1001; /* Higher than store-status but lower than sidebar */
        display: none;
        padding: 10px 0;
        scrollbar-width: thin;
        scrollbar-color: var(--color-primary, #3f51b5) transparent;
    }

    /* Custom scrollbar for webkit browsers */
    #stores-dropdown::-webkit-scrollbar {
        width: 6px;
    }

    #stores-dropdown::-webkit-scrollbar-track {
        background: transparent;
    }

    #stores-dropdown::-webkit-scrollbar-thumb {
        background-color: var(--color-primary, #3f51b5);
        border-radius: 6px;
    }

    #stores-dropdown.show {
        display: block !important; /* Force display with !important */
        animation: dropdownFadeIn 0.4s cubic-bezier(0.25, 1, 0.5, 1);
        visibility: visible !important; /* Ensure visibility */
        opacity: 1 !important; /* Ensure opacity */
        pointer-events: auto !important; /* Ensure clickable */
        transform-origin: top right;
    }

    @keyframes dropdownFadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
            box-shadow: 0 0 0 rgba(0, 0, 0, 0);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
    }

    #stores-dropdown .store-item {
        padding: 10px 15px;
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-right: 3px solid transparent;
        margin: 2px 0;
        animation: slideIn 0.3s ease forwards;
        opacity: 0;
        transform: translateX(10px);
    }

    /* Animation for items appearing */
    @keyframes slideIn {
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Apply delay to each item */
    #stores-dropdown .store-item:nth-child(1) { animation-delay: 0.05s; }
    #stores-dropdown .store-item:nth-child(2) { animation-delay: 0.1s; }
    #stores-dropdown .store-item:nth-child(3) { animation-delay: 0.15s; }
    #stores-dropdown .store-item:nth-child(4) { animation-delay: 0.2s; }
    #stores-dropdown .store-item:nth-child(5) { animation-delay: 0.25s; }
    #stores-dropdown .store-item:nth-child(6) { animation-delay: 0.3s; }
    #stores-dropdown .store-item:nth-child(7) { animation-delay: 0.35s; }
    #stores-dropdown .store-item:nth-child(8) { animation-delay: 0.4s; }
    #stores-dropdown .store-item:nth-child(9) { animation-delay: 0.45s; }
    #stores-dropdown .store-item:nth-child(10) { animation-delay: 0.5s; }

    #stores-dropdown .store-item:hover {
        background-color: var(--color-hover, rgba(63, 81, 181, 0.1));
        border-right-color: var(--color-primary, #3f51b5);
        padding-right: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    #stores-dropdown .store-item.current {
        background-color: var(--color-primary, #3f51b5);
        color: white;
        border-right-color: #fff;
        box-shadow: 0 2px 8px rgba(63, 81, 181, 0.3);
    }

    #stores-dropdown .store-item.message {
        font-style: italic;
        color: #888;
        justify-content: center;
        cursor: default;
    }

    #stores-dropdown .store-item.message:hover {
        background-color: transparent;
    }



    #stores-dropdown .store-item i {
        font-size: 16px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(63, 81, 181, 0.1);
        color: var(--color-primary, #3f51b5);
        transition: all 0.3s ease;
    }

    #stores-dropdown .store-item:hover i {
        transform: scale(1.1);
        background-color: rgba(63, 81, 181, 0.2);
    }

    #stores-dropdown .store-item.current i {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }

    /* Check icon for current store */
    #stores-dropdown .store-item.current i.fa-check {
        background-color: transparent;
        color: rgba(255, 255, 255, 0.8);
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
    }

    /* Dark mode support for stores dropdown */
    [data-theme="dark"] #stores-dropdown {
        background-color: var(--color-secondary, #2c2e31);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    }

    [data-theme="dark"] #stores-dropdown .store-item:hover {
        background-color: var(--color-hover, rgba(63, 81, 181, 0.2));
    }

    [data-theme="dark"] #stores-dropdown .store-item i {
        background-color: rgba(63, 81, 181, 0.2);
    }

    [data-theme="dark"] #stores-dropdown .store-item:hover i {
        background-color: rgba(63, 81, 181, 0.3);
    }

    [data-theme="dark"] #stores-dropdown::-webkit-scrollbar-thumb {
        background-color: var(--color-primary, #3f51b5);
    }

    #store-status .branch-icon {
        color: var(--color-primary, #3f51b5);
        font-size: 22px;
        margin-left: 10px;
        animation: storeIconFloat 3s ease-in-out infinite;
        cursor: pointer;
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    #store-status:hover .branch-icon {
        transform: translateY(-2px) rotate(5deg);
    }

    #store-status .branch-icon.clicked {
        animation: branch-click 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
        transform-origin: center;
    }

    @keyframes branch-click {
        0% { transform: scale(1); }
        40% { transform: scale(1.15); }
        80% { transform: scale(0.95); }
        100% { transform: scale(1); }
    }

    /* Dark mode support */
    [data-theme="dark"] #store-status {
        background-color: var(--color-secondary, #2c2e31);
        border-left: 3px solid var(--color-primary, #3f51b5);
        color: var(--color-fg, #e4e6eb);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    [data-theme="dark"] #store-status .status-text {
        color: var(--color-fg, #e4e6eb);
    }

    [data-theme="dark"] #store-status .branch-icon {
        color: var(--color-primary, #3f51b5);
        animation: storeIconFloat 3s ease-in-out infinite;
    }

    [data-theme="dark"] #store-status .branch-icon.clicked {
        animation: branch-click 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
    }

    [data-theme="dark"] #store-status:hover .branch-icon {
        transform: translateY(-2px) rotate(5deg);
    }

    [data-theme="dark"] #store-status .fa-chevron-down {
        color: var(--color-fg, #e4e6eb);
    }

    /* Media Query for Small Screens */
    @media (max-width: 768px) {
        #sidebar.styled-sidebar {
            width: 70%;
            border-radius: 0;
        }

        .menu-icon {
            right: 15px;
        }

        #sidebar ul li a {
            font-size: 0.8rem;
            padding: 10px 12px;
        }

        .store-name-container {
            font-size: 1em;
            padding: 10px;
        }
    }

    /* Dark Theme for Sidebar */
    [data-theme="dark"] #sidebar.styled-sidebar {
        background: var(--color-secondary);
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.5);
    }

    [data-theme="dark"] #sidebar ul li a,
    [data-theme="dark"] #sidebar ul li span {
        color: var(--color-fg);
    }

    [data-theme="dark"] #sidebar ul li a:hover,
    [data-theme="dark"] #sidebar ul li span:hover {
        background-color: var(--color-hover);
        color: var(--color-fg);
        transform: translateX(-5px);
        padding-right: 23px;
    }

    [data-theme="dark"] #sidebar ul li a:hover i,
    [data-theme="dark"] #sidebar ul li span:hover i {
        transform: scale(1.2);
        color: var(--color-primary);
    }

    [data-theme="dark"] #sidebar ul li a:hover::before,
    [data-theme="dark"] #sidebar ul li span:hover::before {
        transform: scaleY(1);
    }

    /* Dark mode styling for submenu items */
    [data-theme="dark"] #sidebar ul li ul li a {
        background-color: rgba(63, 81, 181, 0.1);
        color: var(--color-fg);
    }

    [data-theme="dark"] #sidebar ul li ul li a:hover {
        background-color: rgba(63, 81, 181, 0.2);
    }

    [data-theme="dark"] #sidebar ul li ul li a.subcategory-link {
        background-color: rgba(63, 81, 181, 0.15);
        border-right: 2px solid var(--color-primary);
    }

    [data-theme="dark"] #sidebar ul li ul li a.subcategory-link:hover {
        background-color: rgba(63, 81, 181, 0.25);
    }

    [data-theme="dark"] .submenu-toggle:hover {
        background-color: rgba(63, 81, 181, 0.2);
    }

    [data-theme="dark"] .submenu-toggle i {
        color: var(--color-fg);
    }

    [data-theme="dark"] .loading-item span {
        color: var(--color-fg);
    }

    [data-theme="dark"] .categories-submenu.open {
        border-right: 2px solid rgba(63, 81, 181, 0.5);
        background-color: rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .categories-submenu::-webkit-scrollbar-thumb {
        background-color: rgba(63, 81, 181, 0.6);
    }

    [data-theme="dark"] #sidebar ul li a.active {
        background: linear-gradient(145deg, var(--color-primary), #5c6bc0);
        color: var(--color-header-text);
        position: relative;
        overflow: hidden;
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        transform: translateX(-5px);
        padding-right: 23px;
        border-radius: 12px 0 0 12px;
        border-right: 4px solid #ffeb3b;
        animation: activeBorder 2s ease-in-out infinite;
    }

    [data-theme="dark"] #sidebar ul li a.active::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        background-color: #ffeb3b;
        transform: scaleY(1);
    }

    [data-theme="dark"] #sidebar ul li a.active::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
        transform: translateX(-100%);
        animation: activeLink 3s ease-in-out infinite;
    }

    [data-theme="dark"] #sidebar ul li a.active i {
        color: var(--color-header-text);
        animation: activeIcon 2s ease-in-out infinite;
        transform-origin: center;
        margin-left: 15px;
        font-size: 1.2em;
        text-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
        position: relative;
        z-index: 2;
    }

    [data-theme="dark"] #sidebar ul li a.active i::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        background: radial-gradient(circle, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        z-index: -1;
        animation: iconGlow 2s ease-in-out infinite;
    }

    [data-theme="dark"] .store-name-container {
        background: linear-gradient(135deg, var(--color-primary), #5c6bc0);
        color: var(--color-header-text);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    }

    [data-theme="dark"] .store-name {
        color: var(--color-header-text);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    [data-theme="dark"] .store-name i {
        color: var(--color-header-text);
    }

    [data-theme="dark"] .menu-icon {
        background: linear-gradient(to right, var(--color-primary), #1abc9c);
        color: var(--color-header-text);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    }

    [data-theme="dark"] .menu-icon:hover {
        background: linear-gradient(to right, #1abc9c, var(--color-primary));
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.7);
    }

    [data-theme="dark"] #sidebar ul li.logout a {
        background-color: #e74c3c;
        color: var(--color-header-text);
    }

    [data-theme="dark"] #sidebar ul li.logout a:hover {
        background-color: #c0392b;
    }

    #sidebar ul li a.theme-toggle-link {
        color: var(--color-fg);
        background-color: transparent;
        padding: 12px 18px;
        text-decoration: none;
        border-radius: 12px;
        transition: background-color var(--transition-duration), transform var(--transition-duration);
        display: flex;
        align-items: center;
    }

    #sidebar ul li a.theme-toggle-link:hover {
        background-color: var(--color-hover);
        transform: scale(1.05);
    }

    #sidebar ul li a.theme-toggle-link i {
        margin-left: 12px;
        transition: transform var(--transition-duration);
    }

    #sidebar ul li a.theme-toggle-link[data-theme="dark"] i {
        transform: rotate(180deg); /* Rotate icon for dark mode */
    }

    /* Animation for Theme Toggle */
    :root {
        --transition-duration: 0.4s;
    }

    [data-theme="dark"],
    [data-theme="light"] {
        transition: background-color var(--transition-duration), color var(--transition-duration);
    }

    .header-container {
        position: fixed; /* جديد: اجعل الشريط ثابت */
        top: 0;          /* جديد */
        left: 0;         /* جديد */
        right: 0;        /* جديد */
        z-index: 1200;   /* جديد */
        display: flex;
        flex-direction: column; /* Updated to align header title and icons */
        align-items: center;    /* Center align header title */
        padding: 10px 20px;
        background-color: var(--color-secondary);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid var(--color-primary);
        margin-bottom: 80px; /* تمت إضافة هذه المسافة لضمان ظهور المحتوى */
    }

    .menu-icon-container,
    .notification-icon-container,
    .theme-toggle-container,
    .account-icon,
    .back-arrow-container {
        color: var(--color-primary); /* لون الأيقونات يتبع لون العنوان */
        background-color: var(--color-bg);
        cursor: pointer;
        font-size: 24px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: background-color 0.3s ease, transform 0.2s ease;
    }

    .menu-icon-container:hover,
    .notification-icon-container:hover,
    .theme-toggle-container:hover,
    .account-icon:hover,
    .back-arrow-container:hover {
        background-color: var(--color-hover); /* لون الهوفر */
        transform: scale(1.1);
    }

    .notification-icon-container {
        position: relative;
    }

    .notification-icon-container .notification-count {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: red;
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 12px;
        font-weight: bold;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        animation: notificationPulse 2s infinite;
    }

    /* Enhanced notification bell animation */
    .notification-icon-container i {
        transition: all 0.3s ease;
    }

    .notification-icon-container:hover i {
        animation: bellRing 0.6s ease-in-out;
        transform-origin: top center;
    }

    .notification-icon-container.new-notification i {
        animation: bellAlert 1s ease-in-out;
        color: #ff6b6b;
    }

    @keyframes notificationPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.8;
        }
    }

    @keyframes bellRing {
        0%, 100% { transform: rotate(0deg); }
        10%, 30%, 50%, 70%, 90% { transform: rotate(-10deg); }
        20%, 40%, 60%, 80% { transform: rotate(10deg); }
    }

    @keyframes bellAlert {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            filter: drop-shadow(0 0 0 transparent);
        }
        25% {
            transform: scale(1.1) rotate(-5deg);
            filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.6));
        }
        50% {
            transform: scale(1.2) rotate(0deg);
            filter: drop-shadow(0 0 12px rgba(255, 107, 107, 0.8));
        }
        75% {
            transform: scale(1.1) rotate(5deg);
            filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.6));
        }
    }

    [data-theme="dark"] .header-container {
        background-color: var(--color-bg);
        border-bottom: 1px solid #58a6ff; /* تم التعديل ليصبح باللون الأزرق في الثيم الدارك */
    }

    [data-theme="dark"] .menu-icon-container,
    [data-theme="dark"] .notification-icon-container,
    [data-theme="dark"] .theme-toggle-container,
    [data-theme="dark"] .account-icon,
    [data-theme="dark"] .back-arrow-container {
        color: var(--color-primary); /* لون الأيقونات في الوضع الداكن */
        background-color: var(--color-secondary);
    }

    [data-theme="dark"] .notification-icon-container .notification-count {
        background-color: #e74c3c;
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.6), 0 2px 4px rgba(0, 0, 0, 0.3);
        animation: notificationPulseDark 2s infinite;
    }

    /* Enhanced dark mode notification animations */
    [data-theme="dark"] .notification-icon-container i {
        color: #58a6ff;
        text-shadow: 0 0 8px rgba(88, 166, 255, 0.3);
        transition: all 0.3s ease;
    }

    [data-theme="dark"] .notification-icon-container:hover i {
        color: #79c0ff;
        text-shadow: 0 0 12px rgba(121, 192, 255, 0.6);
        animation: bellRingDark 0.6s ease-in-out;
        transform-origin: top center;
    }

    [data-theme="dark"] .notification-icon-container.new-notification i {
        animation: bellAlertDark 1s ease-in-out;
        color: #ff7b7b;
        text-shadow: 0 0 15px rgba(255, 123, 123, 0.8);
    }

    @keyframes notificationPulseDark {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.6), 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        50% {
            transform: scale(1.3);
            opacity: 0.9;
            box-shadow: 0 0 20px rgba(231, 76, 60, 0.9), 0 4px 8px rgba(0, 0, 0, 0.4);
        }
    }

    @keyframes bellRingDark {
        0%, 100% {
            transform: rotate(0deg);
            filter: drop-shadow(0 0 8px rgba(88, 166, 255, 0.3));
        }
        10%, 30%, 50%, 70%, 90% {
            transform: rotate(-12deg);
            filter: drop-shadow(0 0 12px rgba(88, 166, 255, 0.6));
        }
        20%, 40%, 60%, 80% {
            transform: rotate(12deg);
            filter: drop-shadow(0 0 12px rgba(88, 166, 255, 0.6));
        }
    }

    @keyframes bellAlertDark {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            filter: drop-shadow(0 0 8px rgba(255, 123, 123, 0.4));
            text-shadow: 0 0 15px rgba(255, 123, 123, 0.8);
        }
        25% {
            transform: scale(1.15) rotate(-8deg);
            filter: drop-shadow(0 0 15px rgba(255, 123, 123, 0.8));
            text-shadow: 0 0 25px rgba(255, 123, 123, 1);
        }
        50% {
            transform: scale(1.3) rotate(0deg);
            filter: drop-shadow(0 0 20px rgba(255, 123, 123, 1));
            text-shadow: 0 0 30px rgba(255, 123, 123, 1);
        }
        75% {
            transform: scale(1.15) rotate(8deg);
            filter: drop-shadow(0 0 15px rgba(255, 123, 123, 0.8));
            text-shadow: 0 0 25px rgba(255, 123, 123, 1);
        }
    }

    .theme-toggle-container i,
    .account-icon i {
        color: var(--color-primary);
    }

    /* في الثيم الداكن */
    [data-theme="dark"] .theme-toggle-container i,
    [data-theme="dark"] .account-icon i {
        color: var(--color-primary); /* لون الأيقونات في الوضع الداكن */
        background-color: var(--color-secondary);    }

    .theme-toggle-container i {
        transition: transform 0.3s ease;
    }

    .theme-toggle-container.animate i {
        animation: rotateThemeIcon 0.5s ease-in-out;
    }

    @keyframes rotateThemeIcon {
        0% {
            transform: rotate(0deg);
        }
        50% {
            transform: rotate(180deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    /* New styles for footer and heart icon */
    .heart-icon {
        display: inline-block;
        color: var(--color-primary);
        font-size: 1rem;
        animation: heartbeat 1.5s infinite;
    }

    @keyframes heartbeat {
        0%, 100% { transform: scale(1) rotate(-10deg); }
        25% { transform: scale(1.2) rotate(-10deg); }
        50% { transform: scale(1) rotate(-10deg); }
        75% { transform: scale(1.2) rotate(-10deg); }
    }

    .footer-text {
        font-family: 'Poppins', sans-serif;
        font-size: 0.9rem;
        font-weight: 600;
    }
</style>

<?php

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}


// Get current file name for active link highlighting
$current_file = basename($_SERVER['PHP_SELF']);

// Get store_id from URL parameter or from local variable (for items.php)
$encrypted_store_id = isset($encrypted_store_id) ? $encrypted_store_id : (isset($_GET['store_id']) ? $_GET['store_id'] : '');
$store_id = null;
$store_name = 'غير محدد';

if (!empty($encrypted_store_id)) {
    require_once 'encryption_functions.php';
    $key = getenv('ENCRYPTION_KEY');
    $store_id = decrypt($encrypted_store_id, $key);

    if ($store_id) {
        // Get store name
        if (!isset($conn)) {
            require_once 'db_connection.php';
        }
        $store_query = "SELECT name FROM stores WHERE store_id = ?";
        $store_stmt = $conn->prepare($store_query);
        $store_stmt->bind_param("i", $store_id);
        $store_stmt->execute();
        $store_result = $store_stmt->get_result();
        if ($store_row = $store_result->fetch_assoc()) {
            $store_name = $store_row['name'];
        }
        $store_stmt->close();
    }
}

// Get unread notifications count for sidebar
if (!isset($unreadCount)) {
    $unreadCount = 0;
    if (isset($_SESSION['account_id'])) {
        $key = getenv('ENCRYPTION_KEY');
        $user_id = decrypt($_SESSION['account_id'], $key);

        // Count unread notifications
        $unread_query = "
            SELECT COUNT(*) as unread_count
            FROM notifications n
            WHERE n.status = 'notread'
        ";

        $unread_params = [];

        if ($store_id) {
            $unread_query .= " AND (n.store_id = ? OR n.store_id IS NULL)";
            $unread_params[] = $store_id;
        }

        if (!isset($conn)) {
            require_once 'db_connection.php';
        }

        $unread_stmt = $conn->prepare($unread_query);
        if (count($unread_params) > 0) {
            $unread_stmt->bind_param(str_repeat('i', count($unread_params)), ...$unread_params);
        }
        $unread_stmt->execute();
        $unread_result = $unread_stmt->get_result();
        $unread_row = $unread_result->fetch_assoc();
        $unreadCount = $unread_row['unread_count'] ?? 0;
        $unread_stmt->close();
    }
}

?>

<?php include_once 'firebase_scripts.php'; ?>
<script>
    // تعريف متغير معرف الفرع المشفر ليكون متاحًا للاستخدام في JavaScript
    var encrypted_store_id = "<?php echo isset($encrypted_store_id) ? $encrypted_store_id : ''; ?>";

    document.addEventListener("DOMContentLoaded", () => {
        const sidebar = document.getElementById("sidebar");
        const menuBtn = document.querySelector('.menu-icon-container'); // Updated selector
        function toggleSidebar() {
            if (sidebar.classList.contains("open")) {
                sidebar.classList.remove("open");
                menuBtn.classList.remove("open");
            } else {
                sidebar.classList.add("open");
                menuBtn.classList.add("open");
            }
        }
        menuBtn.addEventListener("click", toggleSidebar);

        // Always show accounts submenu
        const accountsMenuItem = document.querySelector('#sidebar ul li:nth-child(9)');
        if (accountsMenuItem) {
            const submenu = accountsMenuItem.querySelector('ul');
            if (submenu) {
                submenu.style.maxHeight = '500px';
                submenu.style.overflow = 'visible';
                submenu.style.display = 'block';
                submenu.style.opacity = '1';
            }
        }

        // إدارة قائمة التصنيفات
        const categoriesToggle = document.querySelector('.submenu-toggle');
        const categoriesSubmenu = document.querySelector('.categories-submenu');
        let categoriesLoaded = false;

        if (categoriesToggle && categoriesSubmenu) {
            // إضافة تأثير عند تحويم الماوس
            categoriesToggle.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(63, 81, 181, 0.1)';
            });

            categoriesToggle.addEventListener('mouseleave', function() {
                if (!this.classList.contains('open')) {
                    this.style.backgroundColor = '';
                }
            });

            // تبديل حالة القائمة عند النقر
            categoriesToggle.addEventListener('click', function(e) {
                // منع انتشار الحدث
                e.stopPropagation();

                // تبديل حالة القائمة
                this.classList.toggle('open');
                categoriesSubmenu.classList.toggle('open');

                // تغيير لون الخلفية عند الفتح
                if (this.classList.contains('open')) {
                    this.style.backgroundColor = 'rgba(63, 81, 181, 0.2)';
                } else {
                    this.style.backgroundColor = '';
                }

                // جلب التصنيفات إذا لم يتم تحميلها بعد
                if (!categoriesLoaded) {
                    loadCategories();
                }
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!categoriesToggle.contains(e.target) && !categoriesSubmenu.contains(e.target)) {
                    categoriesToggle.classList.remove('open');
                    categoriesSubmenu.classList.remove('open');
                    categoriesToggle.style.backgroundColor = '';
                }
            });

            // دالة لتحميل التصنيفات من الخادم
            function loadCategories() {
                // الحصول على معرف الفرع من عنوان URL أو من معرف التصنيف
                const urlParams = new URLSearchParams(window.location.search);
                let storeId = urlParams.get('store_id');

                // في صفحة items.php، قد لا يكون هناك معرف فرع في URL، ولكن يمكننا استخدام معرف الفرع المستخرج من التصنيف
                if (!storeId && typeof encrypted_store_id !== 'undefined') {
                    storeId = encrypted_store_id;
                }

                if (!storeId) {
                    categoriesSubmenu.innerHTML = '<li><span>لا يوجد فرع محدد</span></li>';
                    categoriesLoaded = true;
                    return;
                }

                // جلب التصنيفات من الخادم
                fetch(`get_sidebar_categories.php?store_id=${storeId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        categoriesSubmenu.innerHTML = '';

                        if (data.success && data.categories && data.categories.length > 0) {
                            // إضافة التصنيفات إلى القائمة
                            data.categories.forEach(category => {
                                const categoryItem = document.createElement('li');
                                categoryItem.innerHTML = `
                                    <a href="items.php?category_id=${category.encrypted_id}">
                                        <i class="fas fa-tag" style="margin-left: 8px;"></i>
                                        ${category.name}
                                    </a>
                                `;
                                categoriesSubmenu.appendChild(categoryItem);
                            });
                        } else {
                            categoriesSubmenu.innerHTML = '<li><span>لا توجد تصنيفات</span></li>';
                        }

                        categoriesLoaded = true;
                    })
                    .catch(error => {
                        console.error('Error loading categories:', error);
                        categoriesSubmenu.innerHTML = '<li><span>حدث خطأ أثناء تحميل التصنيفات</span></li>';
                        categoriesLoaded = true;
                    });
            }
        }

        // عند النقر خارج الشريط الجانبي والأيقونة يتم إغلاق الشريط وإعادة الأيقونة لحالتها الافتراضية
        document.addEventListener("click", function(event) {
            if (!sidebar.contains(event.target) && !menuBtn.contains(event.target)) {
                sidebar.classList.remove("open");
                menuBtn.classList.remove("open");
            }
        });

        const themeToggleLink = document.getElementById("theme-toggle-link");
        const themeToggleContainer = document.querySelector(".theme-toggle-container");

        // Update link appearance based on the current theme
        const updateThemeLink = () => {
            const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
            themeToggleLink.setAttribute("data-theme", currentTheme);
        };

        // Toggle theme logic with animation
        themeToggleLink.addEventListener("click", (event) => {
            event.preventDefault();
            const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
            const newTheme = currentTheme === "dark" ? "light" : "dark";

            // Add animation class to the toggle button
            themeToggleContainer.classList.add("animate");

            // Remove the animation class after the animation duration
            setTimeout(() => {
                themeToggleContainer.classList.remove("animate");
            }, 500); // Match the duration in CSS

            document.documentElement.setAttribute("data-theme", newTheme);
            localStorage.setItem("theme", newTheme);
            updateThemeLink();
        });

        // Apply stored theme or system preference on load
        const storedTheme = localStorage.getItem("theme");
        if (storedTheme) {
            document.documentElement.setAttribute("data-theme", storedTheme);
        } else if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
            document.documentElement.setAttribute("data-theme", "dark");
        }
        updateThemeLink();

        // Handle store status indicator and stores dropdown
        const storeStatus = document.getElementById('store-status');
        const storesDropdown = document.getElementById('stores-dropdown');

        if (storeStatus) {
            // Keep store status always visible
            storeStatus.style.display = 'flex';
            storeStatus.style.opacity = '1';

            // No need for scroll event listener as position:fixed in CSS already keeps it fixed
            // Just set the initial positions once
            storeStatus.style.top = '140px';
            storeStatus.style.right = '15px';

            // Initialize dropdown position
            storesDropdown.style.top = '180px';
            storesDropdown.style.right = '15px';



            // Toggle dropdown when clicking on store status
            storeStatus.addEventListener('click', function(e) {
                // Toggle dropdown visibility with direct style manipulation
                if (storesDropdown.style.display === 'block') {
                    storesDropdown.style.display = 'none';
                    storesDropdown.classList.remove('show');
                } else {
                    storesDropdown.style.display = 'block';
                    storesDropdown.classList.add('show');

                    // Ensure dropdown is positioned correctly
                    storesDropdown.style.top = '180px';
                    storesDropdown.style.right = '15px';

                    // Load stores list
                    loadStoresList();
                }

                // Add an enhanced animation when clicked
                const branchIcon = storeStatus.querySelector('.branch-icon');
                if (branchIcon) {
                    // Remove the class first to ensure animation can be triggered again
                    branchIcon.classList.remove('clicked');

                    // Force a reflow to ensure the animation restarts
                    void branchIcon.offsetWidth;

                    // Add the class to trigger the animation
                    branchIcon.classList.add('clicked');

                    // Remove the class after animation completes
                    setTimeout(() => {
                        branchIcon.classList.remove('clicked');
                    }, 600); // Match the duration in CSS (0.6s = 600ms)
                }

                // Prevent event from bubbling up
                e.stopPropagation();
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!storeStatus.contains(e.target) && !storesDropdown.contains(e.target) && storesDropdown.style.display === 'block') {
                    storesDropdown.style.display = 'none';
                    storesDropdown.classList.remove('show');
                }
            });



            // Function to load stores list
            function loadStoresList() {
                // Get current store ID from URL
                const urlParams = new URLSearchParams(window.location.search);
                const currentStoreId = urlParams.get('store_id');

                // Fetch stores list from API
                fetch(`get_stores_list.php${currentStoreId ? `?current_store_id=${currentStoreId}` : ''}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Clear dropdown content first
                        storesDropdown.innerHTML = '';

                        if (data.success && data.stores && data.stores.length > 0) {
                            // Add stores to dropdown
                            data.stores.forEach(store => {
                                const storeItem = document.createElement('div');
                                storeItem.className = `store-item${store.current ? ' current' : ''}`;
                                storeItem.innerHTML = `
                                    <i class="fas fa-store"></i>
                                    <span>${store.name}</span>
                                    ${store.current ? '<i class="fas fa-check" style="margin-right: auto;"></i>' : ''}
                                `;

                                // Add click event to switch store
                                if (!store.current) {
                                    storeItem.addEventListener('click', function() {
                                        switchStore(store.id, store.name);
                                    });
                                }

                                storesDropdown.appendChild(storeItem);
                            });

                            // If only one store, show message
                            if (data.stores.length === 1) {
                                const messageItem = document.createElement('div');
                                messageItem.className = 'store-item message';
                                messageItem.innerHTML = '<span>لا توجد فروع أخرى متاحة</span>';
                                storesDropdown.appendChild(messageItem);
                            }
                        } else {
                            storesDropdown.innerHTML = '<div class="store-item message"><span>لا توجد فروع متاحة</span></div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading stores:', error);
                        storesDropdown.innerHTML = '<div class="store-item message"><span>حدث خطأ أثناء تحميل الفروع</span></div>';
                    });
            }

            // Function to switch to a different store
            function switchStore(storeId, storeName) {
                // Get current URL and replace store_id parameter
                const currentUrl = new URL(window.location.href);
                const currentPath = currentUrl.pathname;
                const currentParams = new URLSearchParams(window.location.search);

                // Update store_id parameter
                currentParams.set('store_id', storeId);

                // Redirect to the same page with new store_id
                window.location.href = `${currentPath}?${currentParams.toString()}`;
            }
        }

        // Handle logout confirmation
        const logoutLink = document.querySelector('.logout a');
        if (logoutLink) {
            logoutLink.addEventListener('click', (event) => {
                event.preventDefault(); // Prevent default link behavior
                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: "سيتم تسجيل خروجك من الحساب!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، تسجيل الخروج',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = logoutLink.href; // Redirect to logout URL
                    }
                });
            });
        }
    });
</script>

<div class="header-container">
    <!-- New Header Title -->
    <div class="header-title" style="width: 100%; text-align: center; font-size: 2rem; font-weight: bold; color: var(--color-primary);">
        Elwaled Market
    </div>
    <!-- Icons Row -->
    <div class="icons-container" style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin-top: 10px;">
        <!-- Back Arrow Icon -->
        <div class="back-arrow-container" onclick="window.history.back();" style="cursor:pointer;display:flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background-color:var(--color-bg);margin-left:10px;">
            <i class="fas fa-arrow-right" style="font-size:22px;color:var(--color-primary);"></i>
        </div>
        <!-- Sidebar Toggle Icon -->
        <div class="menu-icon-container" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </div>
        <!-- Notification Bell Icon -->
        <div class="notification-icon-container" onclick="showNotifications()">
            <i class="fas fa-bell"></i>
            <?php if ($unreadCount > 0): ?>
                <span class="notification-count"><?= $unreadCount ?></span>
            <?php else: ?>
                <span class="notification-count" style="display: none;">0</span>
            <?php endif; ?>
        </div>
        <!-- Theme Toggle Button -->
        <div class="theme-toggle-container">
            <a href="#" id="theme-toggle-link" class="theme-toggle-link">
                <i class="fas fa-adjust"></i>
            </a>
        </div>
        <!-- Account Icon -->
        <div class="account-icon">
            <a href="profile.php?store_id=<?= urlencode($encrypted_store_id); ?>">
                <i class="fas fa-user-circle"></i>
            </a>
        </div>
    </div>
</div>

<!-- Store Status Indicator -->
<?php if (isset($store_name) && !empty($store_name)): ?>
<div id="store-status">
    <i class="fas fa-store branch-icon"></i>
    <span class="status-text">الفرع: <?php echo htmlspecialchars($store_name); ?></span>
    <i class="fas fa-chevron-down" style="font-size: 12px; margin-right: 5px;"></i>
</div>

<!-- Stores Dropdown Menu - Moved outside to avoid nesting issues -->
<div id="stores-dropdown">
    <!-- Will be populated with JavaScript -->
</div>


<?php endif; ?>

<div id="sidebar" class="styled-sidebar">
    <!-- Store name display -->
    <div class="store-name-container">
        <h3 class="store-name"><i class="fas fa-store" style="margin-left: 8px;"></i> الفرع: <?php echo htmlspecialchars($store_name); ?></h3>
    </div>
    <ul id="navLinks">
        <li><a href="stores.php" class="<?php echo $current_file === 'stores.php' ? 'active' : ''; ?>"><i class="fas fa-home"></i> الرئيسية</a></li>
        <li>
    <a href="store_shortcuts.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?= $current_file === 'store_shortcuts.php' ? 'active' : ''; ?>">
        <i class="fas fa-cogs"></i> ادارة
    </a>
</li>

        <li><a href="item_reports.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'item_reports.php' ? 'active' : ''; ?>"><i class="fas fa-chart-line"></i> التقارير</a></li>
        <li class="has-submenu">
            <div class="menu-item-container">
                <a href="categories.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'categories.php' ? 'active' : ''; ?>"><i class="fas fa-th-list"></i> التصنيفات</a>
                <span class="submenu-toggle"><i class="fas fa-chevron-down"></i></span>
            </div>
            <ul class="categories-submenu" style="max-height: 0; overflow: hidden;">
                <!-- سيتم ملء هذه القائمة بالتصنيفات عبر JavaScript -->
                <li class="loading-item"><span><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</span></li>
            </ul>
            <?php if ($current_file === 'items.php' && isset($category_name) && !empty($category_name)): ?>
            <ul style="max-height: 500px; overflow: visible; display: block; opacity: 1; margin-top: 5px; padding-right: 15px;">
                <li><a href="items.php?category_id=<?php echo urlencode($encrypted_category_id); ?>" class="active subcategory-link">
                    <i class="fas fa-tag" style="margin-left: 8px;"></i>
                    <?php echo htmlspecialchars($category_name); ?>
                </a></li>
            </ul>
            <?php endif; ?>
        </li>
        <li><a href="item_store.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'item_store.php' ? 'active' : ''; ?>"><i class="fas fa-boxes"></i> الأصناف</a></li>
        <li><a href="purchase_invoices.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'purchase_invoices.php' ? 'active' : ''; ?>"><i class="fas fa-file-invoice-dollar"></i> فواتير الشراء</a></li>
        <li><a href="wholesale_invoices.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'wholesale_invoices.php' ? 'active' : ''; ?>"><i class="fas fa-shopping-cart"></i> فواتير البيع بالجملة</a></li>
        <li><a href="inventory.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'inventory.php' ? 'active' : ''; ?>"><i class="fas fa-warehouse"></i> الجرد</a></li>
        <li>
            <span><i class="fas fa-user-cog"></i> الحسابات</span>
            <ul>
                <li><a href="accounts.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'accounts.php' ? 'active' : ''; ?>">إدارة</a></li>
                <li><a href="requested_accounts.php" class="<?php echo $current_file === 'requested_accounts.php' ? 'active' : ''; ?>">طلبات</a></li>
                <?php if (isset($permissions_system) && $permissions_system->hasPermission('accounts', 'manage_permissions')): ?>
                <li><a href="manage_permissions.php" class="<?php echo $current_file === 'manage_permissions.php' ? 'active' : ''; ?>">الصلاحيات</a></li>
                <?php endif; ?>
            </ul>
        </li>
        <li><a href="transfer_items.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'transfer_items.php' ? 'active' : ''; ?>"><i class="fas fa-exchange-alt"></i> نقل الأصناف</a></li>
        <li><a href="expired_items.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'expired_items.php' ? 'active' : ''; ?>"><i class="fas fa-hourglass-end"></i> صلاحيات الأصناف</a></li>
        <li><a href="expenses.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'expenses.php' ? 'active' : ''; ?>"><i class="fas fa-money-bill-wave"></i> المصاريف</a></li>

        <li>
            <a href="shift_closures.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'shift_closures.php' ? 'active' : ''; ?>">
                <i class="fas fa-cash-register"></i> تقفيل الورديات
            </a>
        </li>
        <li>
            <a href="balance_transfers.php?store_id=<?php echo urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'balance_transfers.php' ? 'active' : ''; ?>">
                <i class="fas fa-wallet"></i> تحويلات الرصيد
            </a>
        </li>
        <li>
            <a href="project_summary.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'project_summary.php' ? 'active' : ''; ?>">
                <i class="fas fa-info-circle"></i> شرح النظام
            </a>
        </li>
        <li>
            <a href="notifications_page.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'notifications_page.php' ? 'active' : ''; ?>">
                <i class="fas fa-bell"></i> الإشعارات
                <?php if ($unreadCount > 0): ?>
                    <span class="notification-badge"><?= $unreadCount ?></span>
                <?php else: ?>
                    <span class="notification-badge" style="display: none;">0</span>
                <?php endif; ?>
            </a>
        </li>
        <li>
            <a href="send_notifications.php?store_id=<?= urlencode($encrypted_store_id); ?>" class="<?php echo $current_file === 'send_notifications.php' ? 'active' : ''; ?>">
                <i class="fas fa-paper-plane"></i> إرسال الإشعارات
            </a>
        </li>
        <li class="logout"><a href="logout.php" class="<?php echo $current_file === 'logout.php' ? 'active' : ''; ?>"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
    </ul>
</div> <!-- End of #sidebar -->

<!-- Updated global page footer with new font and heart icon -->
<div class="page-footer footer-text" style="position: fixed; bottom: 0; left: 0; right: 0; background-color: var(--color-secondary); color: var(--color-primary); text-align: center; padding: 10px 0; border-top: 2px solid var(--color-primary); box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);">
    Developed with <i class="fas fa-heart heart-icon"></i> by Eyad Waled
</div>

<script>
// Force show notification counters for testing
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar loaded, checking notification counters...');

    // Find all notification counters
    const counters = document.querySelectorAll('.notification-count');
    const badges = document.querySelectorAll('.notification-badge');

    console.log('Found counters:', counters.length);
    console.log('Found badges:', badges.length);

    // Force show counters
    counters.forEach((counter, index) => {
        console.log(`Counter ${index}:`, counter);
        counter.style.display = 'flex';
        counter.style.visibility = 'visible';
        counter.style.opacity = '1';
    });

    // Add enhanced notification bell animations
    const notificationContainer = document.querySelector('.notification-icon-container');
    if (notificationContainer) {
        // Add click animation
        notificationContainer.addEventListener('click', function() {
            this.classList.add('new-notification');
            setTimeout(() => {
                this.classList.remove('new-notification');
            }, 1000);
        });

        // Trigger alert animation when new notifications arrive
        window.triggerNotificationAlert = function() {
            notificationContainer.classList.add('new-notification');
            setTimeout(() => {
                notificationContainer.classList.remove('new-notification');
            }, 1000);
        };

        // Enhanced pulse for dark mode
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        if (isDarkMode) {
            notificationContainer.classList.add('dark-mode-enhanced');
        }

        // Listen for theme changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                    if (isDark) {
                        notificationContainer.classList.add('dark-mode-enhanced');
                    } else {
                        notificationContainer.classList.remove('dark-mode-enhanced');
                    }
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    }

    badges.forEach((badge, index) => {
        console.log(`Badge ${index}:`, badge);
        badge.style.display = 'inline-block';
        badge.style.visibility = 'visible';
        badge.style.opacity = '1';
    });
});
</script>
