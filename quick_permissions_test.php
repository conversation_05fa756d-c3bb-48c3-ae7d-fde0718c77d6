<?php
/**
 * اختبار سريع للصلاحيات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>اختبار سريع للصلاحيات</h1>";

try {
    // تضمين الملفات المطلوبة
    include_once 'db_connection.php';
    include_once 'encryption_functions.php';
    
    // تحميل متغيرات البيئة
    if (file_exists(__DIR__ . '/.env')) {
        $dotenv = parse_ini_file(__DIR__ . '/.env');
        foreach ($dotenv as $key => $value) {
            putenv("$key=$value");
        }
    }
    
    $key = getenv('ENCRYPTION_KEY');
    
    // محاكاة تسجيل دخول مدير
    if (!isset($_SESSION['account_id'])) {
        $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
        $admin_result = $conn->query($admin_query);
        
        if ($admin_result && $admin_result->num_rows > 0) {
            $admin = $admin_result->fetch_assoc();
            $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
            $_SESSION['username'] = 'admin_test';
            $_SESSION['role'] = 'admin';
            echo "<p style='color: green;'>✅ تم تسجيل دخول المدير بنجاح</p>";
        }
    }
    
    // تضمين نظام الصلاحيات
    include_once 'permissions_system.php';
    
    // إنشاء نظام الصلاحيات
    $permissions_system = new PermissionsSystem($conn, $key);
    echo "<p style='color: green;'>✅ تم إنشاء نظام الصلاحيات بنجاح</p>";
    
    // اختبار فحص المدير
    $is_admin = $permissions_system->isAdmin();
    echo "<p style='color: " . ($is_admin ? 'green' : 'red') . ";'>";
    echo ($is_admin ? '✅' : '❌') . " فحص المدير: " . ($is_admin ? 'نعم' : 'لا');
    echo "</p>";
    
    // اختبار صلاحية إدارة الحسابات
    $can_manage_accounts = $permissions_system->hasPermission('accounts', 'manage');
    echo "<p style='color: " . ($can_manage_accounts ? 'green' : 'red') . ";'>";
    echo ($can_manage_accounts ? '✅' : '❌') . " صلاحية إدارة الحسابات: " . ($can_manage_accounts ? 'مسموح' : 'غير مسموح');
    echo "</p>";
    
    // اختبار منح صلاحية
    echo "<h2>اختبار منح صلاحية</h2>";
    
    // البحث عن مستخدم آخر للاختبار
    $user_query = "SELECT account_id FROM accounts WHERE role != 'admin' AND status = 'active' LIMIT 1";
    $user_result = $conn->query($user_query);
    
    if ($user_result && $user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        $test_user_id = $user['account_id'];
        
        try {
            $result = $permissions_system->grantUserPermission($test_user_id, 'purchase_invoices', 'view');
            echo "<p style='color: green;'>✅ تم منح صلاحية بنجاح للمستخدم $test_user_id</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في منح الصلاحية: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لم يتم العثور على مستخدم للاختبار</p>";
    }
    
    // اختبار دوال المساعدة
    echo "<h2>اختبار دوال المساعدة</h2>";
    
    include_once 'auth_check.php';
    
    $has_any = hasAnyPermission('accounts', ['manage']);
    echo "<p style='color: " . ($has_any ? 'green' : 'red') . ";'>";
    echo ($has_any ? '✅' : '❌') . " دالة hasAnyPermission: " . ($has_any ? 'تعمل' : 'لا تعمل');
    echo "</p>";
    
    echo "<h2>معلومات إضافية</h2>";
    echo "<p><strong>معرف الحساب المشفر:</strong> " . ($_SESSION['account_id'] ?? 'غير موجود') . "</p>";
    echo "<p><strong>معرف الحساب المفكوك:</strong> " . (isset($_SESSION['account_id']) ? decrypt($_SESSION['account_id'], $key) : 'غير موجود') . "</p>";
    echo "<p><strong>الدور:</strong> " . ($_SESSION['role'] ?? 'غير موجود') . "</p>";
    
    // فحص قاعدة البيانات
    echo "<h2>فحص قاعدة البيانات</h2>";
    
    $admin_permissions_query = "
        SELECT COUNT(*) as count 
        FROM role_permissions rp 
        JOIN roles r ON rp.role_id = r.role_id 
        WHERE r.role_name = 'admin' AND rp.granted = TRUE
    ";
    $result = $conn->query($admin_permissions_query);
    $admin_permissions_count = $result->fetch_assoc()['count'];
    echo "<p>عدد صلاحيات المدير في قاعدة البيانات: <strong>$admin_permissions_count</strong></p>";
    
    // فحص صلاحية محددة في قاعدة البيانات
    $specific_permission_query = "
        SELECT COUNT(*) as count 
        FROM role_permissions rp 
        JOIN roles r ON rp.role_id = r.role_id 
        JOIN modules m ON rp.module_id = m.module_id 
        JOIN permissions p ON rp.permission_id = p.permission_id 
        WHERE r.role_name = 'admin' 
        AND m.module_name = 'accounts' 
        AND p.permission_name = 'manage' 
        AND rp.granted = TRUE
    ";
    $result = $conn->query($specific_permission_query);
    $specific_count = $result->fetch_assoc()['count'];
    echo "<p>صلاحية إدارة الحسابات للمدير في قاعدة البيانات: <strong>$specific_count</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>روابط الاختبار</h2>";
echo "<a href='manage_permissions.php' style='display: inline-block; background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin: 5px;'>إدارة الصلاحيات</a>";
echo "<a href='test_new_permissions_system.php' style='display: inline-block; background: #27ae60; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin: 5px;'>اختبار النظام</a>";
echo "<a href='stores.php' style='display: inline-block; background: #f39c12; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin: 5px;'>الصفحة الرئيسية</a>";

?>
