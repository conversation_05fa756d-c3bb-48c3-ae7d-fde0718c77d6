<?php
// تضمين ملف الاتصال
include_once 'db_connection.php';

echo "========================================\n";
echo "   تحليل شامل لقاعدة البيانات\n";
echo "   elwaled_market_v3\n";
echo "========================================\n\n";

// فحص الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error . "\n");
}

echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";

// 1. عرض جميع الجداول الموجودة
echo "1️⃣ الجداول الموجودة في قاعدة البيانات:\n";
echo "==========================================\n";
$result = $conn->query("SHOW TABLES");
$existing_tables = [];
$table_count = 0;

if ($result) {
    while ($row = $result->fetch_array()) {
        $table_count++;
        $existing_tables[] = $row[0];
        echo sprintf("%2d. %s\n", $table_count, $row[0]);
    }
} else {
    echo "❌ خطأ في جلب الجداول: " . $conn->error . "\n";
}

echo "\n📊 إجمالي عدد الجداول: $table_count\n\n";

// 2. تحليل جداول نظام الصلاحيات المطلوبة
echo "2️⃣ تحليل جداول نظام الصلاحيات:\n";
echo "=================================\n";

$required_permissions_tables = [
    'roles' => 'جدول الأدوار والمناصب',
    'modules' => 'جدول وحدات النظام', 
    'permissions' => 'جدول الصلاحيات المختلفة',
    'module_permissions' => 'ربط الوحدات بالصلاحيات المطلوبة',
    'role_permissions' => 'ربط الأدوار بالصلاحيات',
    'user_permissions' => 'صلاحيات خاصة للمستخدمين',
    'user_stores' => 'ربط المستخدمين بالفروع',
    'permission_logs' => 'سجل تغييرات الصلاحيات'
];

$missing_tables = [];
$existing_permissions_tables = [];

foreach ($required_permissions_tables as $table => $description) {
    if (in_array($table, $existing_tables)) {
        echo "✅ $table - $description\n";
        $existing_permissions_tables[] = $table;
    } else {
        echo "❌ $table - $description (غير موجود)\n";
        $missing_tables[] = $table;
    }
}

echo "\n📈 جداول الصلاحيات الموجودة: " . count($existing_permissions_tables) . "/" . count($required_permissions_tables) . "\n";

if (!empty($missing_tables)) {
    echo "⚠️  الجداول المفقودة: " . implode(', ', $missing_tables) . "\n";
}

echo "\n";

// 3. تحليل تفصيلي للجداول الموجودة
echo "3️⃣ التحليل التفصيلي للجداول:\n";
echo "==============================\n";

foreach ($existing_permissions_tables as $table) {
    echo "\n🔍 تحليل جدول: $table\n";
    echo str_repeat("-", 30) . "\n";
    
    // هيكل الجدول
    $structure = $conn->query("DESCRIBE $table");
    if ($structure) {
        echo "📋 أعمدة الجدول:\n";
        $column_count = 0;
        while ($row = $structure->fetch_assoc()) {
            $column_count++;
            echo sprintf("   %2d. %-20s %-15s", $column_count, $row['Field'], "(" . $row['Type'] . ")");
            
            $attributes = [];
            if ($row['Key'] == 'PRI') $attributes[] = "PRIMARY KEY";
            if ($row['Key'] == 'MUL') $attributes[] = "FOREIGN KEY";
            if ($row['Null'] == 'NO') $attributes[] = "NOT NULL";
            if ($row['Default'] !== null) $attributes[] = "DEFAULT: " . $row['Default'];
            if ($row['Extra']) $attributes[] = $row['Extra'];
            
            if (!empty($attributes)) {
                echo " [" . implode(", ", $attributes) . "]";
            }
            echo "\n";
        }
        echo "   📊 إجمالي الأعمدة: $column_count\n";
    }
    
    // عدد السجلات
    $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
    if ($count_result) {
        $count_row = $count_result->fetch_assoc();
        echo "   📈 عدد السجلات: " . $count_row['count'] . "\n";
        
        // عرض بعض البيانات إذا كانت موجودة
        if ($count_row['count'] > 0 && $count_row['count'] <= 10) {
            echo "   📄 عينة من البيانات:\n";
            $sample = $conn->query("SELECT * FROM $table LIMIT 5");
            if ($sample) {
                $row_num = 0;
                while ($data_row = $sample->fetch_assoc()) {
                    $row_num++;
                    echo "      السجل $row_num: ";
                    $values = [];
                    foreach ($data_row as $key => $value) {
                        $values[] = "$key=" . (strlen($value) > 20 ? substr($value, 0, 20) . "..." : $value);
                    }
                    echo implode(", ", array_slice($values, 0, 3)) . "\n";
                }
            }
        }
    }
}

// 4. تحليل جدول accounts
echo "\n4️⃣ تحليل جدول accounts:\n";
echo "========================\n";

if (in_array('accounts', $existing_tables)) {
    echo "✅ جدول accounts موجود\n";

    // فحص هيكل الجدول
    $structure = $conn->query("DESCRIBE accounts");
    $has_access_type = false;
    $columns = [];

    if ($structure) {
        echo "📋 أعمدة جدول accounts:\n";
        $col_num = 0;
        while ($row = $structure->fetch_assoc()) {
            $col_num++;
            $columns[] = $row['Field'];
            echo sprintf("   %2d. %-20s %-15s", $col_num, $row['Field'], "(" . $row['Type'] . ")");

            if ($row['Field'] == 'access_type') {
                $has_access_type = true;
                echo " ✅ [عمود نوع الوصول]";
            }
            if ($row['Key'] == 'PRI') echo " [PRIMARY KEY]";
            echo "\n";
        }
    }

    // فحص عمود access_type
    if ($has_access_type) {
        echo "✅ عمود access_type موجود\n";

        // فحص قيم access_type
        $access_types = $conn->query("SELECT access_type, COUNT(*) as count FROM accounts GROUP BY access_type");
        if ($access_types) {
            echo "📊 توزيع أنواع الوصول:\n";
            while ($row = $access_types->fetch_assoc()) {
                echo "   - " . ($row['access_type'] ?: 'NULL') . ": " . $row['count'] . " مستخدم\n";
            }
        }
    } else {
        echo "❌ عمود access_type غير موجود - يحتاج إضافة\n";
    }

    // إحصائيات المستخدمين
    $stats_queries = [
        'إجمالي المستخدمين' => "SELECT COUNT(*) as count FROM accounts",
        'المستخدمين النشطين' => "SELECT COUNT(*) as count FROM accounts WHERE status = 'active'",
        'المديرين' => "SELECT COUNT(*) as count FROM accounts WHERE role = 'admin'",
        'المستخدمين العاديين' => "SELECT COUNT(*) as count FROM accounts WHERE role = 'user'"
    ];

    echo "📈 إحصائيات المستخدمين:\n";
    foreach ($stats_queries as $label => $query) {
        $result = $conn->query($query);
        if ($result) {
            $row = $result->fetch_assoc();
            echo "   - $label: " . $row['count'] . "\n";
        }
    }
} else {
    echo "❌ جدول accounts غير موجود\n";
}

// 5. تحليل جدول stores
echo "5️⃣ تحليل جدول stores:\n";
echo "======================\n";

if (in_array('stores', $existing_tables)) {
    echo "✅ جدول stores موجود\n";

    $count = $conn->query("SELECT COUNT(*) as count FROM stores");
    if ($count) {
        $count_row = $count->fetch_assoc();
        echo "📈 عدد الفروع: " . $count_row['count'] . "\n";

        // عرض أسماء الفروع
        if ($count_row['count'] > 0) {
            $stores = $conn->query("SELECT * FROM stores LIMIT 5");
            if ($stores) {
                echo "📄 عينة من الفروع:\n";
                while ($store = $stores->fetch_assoc()) {
                    $store_info = [];
                    foreach ($store as $key => $value) {
                        $store_info[] = "$key=" . (strlen($value) > 15 ? substr($value, 0, 15) . "..." : $value);
                    }
                    echo "   - " . implode(", ", array_slice($store_info, 0, 3)) . "\n";
                }
            }
        }
    }
} else {
    echo "❌ جدول stores غير موجود\n";
}

echo "\n";

// 6. فحص ترميز الجداول
echo "6️⃣ فحص ترميز الجداول:\n";
echo "======================\n";

$charset_query = "SELECT TABLE_NAME, TABLE_COLLATION, ENGINE
                  FROM information_schema.TABLES
                  WHERE TABLE_SCHEMA = 'elwaled_market_v3'
                  ORDER BY TABLE_NAME";
$charset_result = $conn->query($charset_query);

if ($charset_result) {
    echo "📋 ترميز ومحرك الجداول:\n";
    $utf8_count = 0;
    $other_count = 0;

    while ($row = $charset_result->fetch_assoc()) {
        $table_name = $row['TABLE_NAME'];
        $collation = $row['TABLE_COLLATION'];
        $engine = $row['ENGINE'];

        echo sprintf("   %-25s %-25s %s", $table_name, $collation, $engine);

        if (strpos($collation, 'utf8mb4') !== false) {
            echo " ✅";
            $utf8_count++;
        } else {
            echo " ⚠️";
            $other_count++;
        }
        echo "\n";
    }

    echo "\n📊 إحصائيات الترميز:\n";
    echo "   - جداول UTF8MB4: $utf8_count\n";
    echo "   - جداول أخرى: $other_count\n";
}

// 7. مقارنة مع ملف التكوين
echo "\n7️⃣ مقارنة مع متطلبات ملف التكوين:\n";
echo "=====================================\n";

$setup_requirements = [
    'جداول نظام الصلاحيات' => count($required_permissions_tables),
    'جداول موجودة' => count($existing_permissions_tables),
    'جداول مفقودة' => count($missing_tables)
];

foreach ($setup_requirements as $item => $count) {
    echo "📊 $item: $count\n";
}

if (empty($missing_tables)) {
    echo "\n✅ جميع جداول نظام الصلاحيات موجودة!\n";
} else {
    echo "\n⚠️ يجب تشغيل ملف التكوين لإنشاء الجداول المفقودة:\n";
    foreach ($missing_tables as $table) {
        echo "   - $table\n";
    }
}

// 8. خلاصة التحليل
echo "\n8️⃣ خلاصة التحليل:\n";
echo "==================\n";

$analysis_summary = [
    'إجمالي الجداول' => count($existing_tables),
    'جداول الصلاحيات الموجودة' => count($existing_permissions_tables),
    'جداول الصلاحيات المطلوبة' => count($required_permissions_tables),
    'نسبة الاكتمال' => round((count($existing_permissions_tables) / count($required_permissions_tables)) * 100, 1) . '%'
];

foreach ($analysis_summary as $metric => $value) {
    echo "📈 $metric: $value\n";
}

echo "\n========================================\n";
echo "   انتهى التحليل الشامل\n";
echo "========================================\n";

$conn->close();
?>
