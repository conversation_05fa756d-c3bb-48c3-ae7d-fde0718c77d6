<?php
/**
 * ملف تشخيص أخطاء نظام الصلاحيات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تشخيص نظام الصلاحيات</h1>";

try {
    echo "<h2>1. اختبار تضمين الملفات</h2>";

    // بدء الجلسة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ تم بدء الجلسة<br>";

    // تضمين قاعدة البيانات
    include_once 'db_connection.php';
    echo "✅ تم تضمين قاعدة البيانات<br>";

    // تضمين دوال التشفير
    include_once 'encryption_functions.php';
    echo "✅ تم تضمين دوال التشفير<br>";

    // تحميل متغيرات البيئة
    if (file_exists(__DIR__ . '/.env')) {
        $dotenv = parse_ini_file(__DIR__ . '/.env');
        foreach ($dotenv as $key => $value) {
            putenv("$key=$value");
        }
        echo "✅ تم تحميل متغيرات البيئة<br>";
    }

    $key = getenv('ENCRYPTION_KEY');
    echo "✅ تم الحصول على مفتاح التشفير<br>";

    echo "<h2>2. اختبار قاعدة البيانات</h2>";

    // اختبار الاتصال بقاعدة البيانات
    if ($conn->ping()) {
        echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
    } else {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }

    // اختبار وجود الجداول
    $required_tables = ['modules', 'permissions', 'roles', 'role_permissions', 'user_permissions', 'permission_logs'];
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "✅ جدول $table موجود<br>";
        } else {
            throw new Exception("جدول $table غير موجود");
        }
    }

    echo "<h2>3. اختبار نظام الصلاحيات</h2>";

    // محاكاة تسجيل دخول مدير
    if (!isset($_SESSION['account_id'])) {
        $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
        $admin_result = $conn->query($admin_query);

        if ($admin_result && $admin_result->num_rows > 0) {
            $admin = $admin_result->fetch_assoc();
            $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
            $_SESSION['username'] = 'admin_test';
            $_SESSION['role'] = 'admin';
            echo "✅ تم محاكاة تسجيل دخول المدير<br>";
        } else {
            throw new Exception('لا يوجد حساب مدير نشط');
        }
    }

    // تضمين نظام الصلاحيات
    include_once 'permissions_system.php';
    echo "✅ تم تضمين نظام الصلاحيات<br>";

    // إنشاء مثيل من النظام
    if (!isset($permissions_system)) {
        $permissions_system = new PermissionsSystem($conn, $key);
    }
    echo "✅ تم إنشاء مثيل نظام الصلاحيات<br>";

    // اختبار فحص صلاحية المدير
    $is_admin = $permissions_system->isAdmin();
    if ($is_admin) {
        echo "✅ المستخدم الحالي مدير نظام<br>";
    } else {
        echo "❌ المستخدم الحالي ليس مدير نظام<br>";
    }

    // اختبار فحص صلاحية محددة
    $has_view_accounts = $permissions_system->hasPermission('accounts', 'view');
    if ($has_view_accounts) {
        echo "✅ المستخدم لديه صلاحية عرض الحسابات<br>";
    } else {
        echo "❌ المستخدم ليس لديه صلاحية عرض الحسابات<br>";
    }

    echo "<h2>4. اختبار auth_check.php</h2>";

    // اختبار تضمين auth_check
    ob_start();
    include 'auth_check.php';
    $auth_output = ob_get_clean();

    if (empty($auth_output)) {
        echo "✅ ملف auth_check.php يعمل بدون أخطاء<br>";
    } else {
        echo "❌ ملف auth_check.php يحتوي على مخرجات: " . htmlspecialchars($auth_output) . "<br>";
    }

    // اختبار الدوال المساعدة
    if (function_exists('checkPagePermission')) {
        echo "✅ دالة checkPagePermission متاحة<br>";
    } else {
        echo "❌ دالة checkPagePermission غير متاحة<br>";
    }

    if (function_exists('requireAdmin')) {
        echo "✅ دالة requireAdmin متاحة<br>";
    } else {
        echo "❌ دالة requireAdmin غير متاحة<br>";
    }

    echo "<h2>5. إحصائيات النظام</h2>";

    $stats = [
        'الوحدات' => $conn->query("SELECT COUNT(*) as count FROM modules")->fetch_assoc()['count'],
        'الصلاحيات' => $conn->query("SELECT COUNT(*) as count FROM permissions")->fetch_assoc()['count'],
        'الأدوار' => $conn->query("SELECT COUNT(*) as count FROM roles")->fetch_assoc()['count'],
        'صلاحيات الأدوار' => $conn->query("SELECT COUNT(*) as count FROM role_permissions")->fetch_assoc()['count'],
        'صلاحيات المستخدمين' => $conn->query("SELECT COUNT(*) as count FROM user_permissions")->fetch_assoc()['count'],
        'سجل الصلاحيات' => $conn->query("SELECT COUNT(*) as count FROM permission_logs")->fetch_assoc()['count']
    ];

    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العنصر</th><th>العدد</th></tr>";
    foreach ($stats as $item => $count) {
        echo "<tr><td>$item</td><td>$count</td></tr>";
    }
    echo "</table>";

    echo "<h2>✅ جميع الاختبارات نجحت!</h2>";
    echo "<p>نظام الصلاحيات يعمل بشكل صحيح ولا توجد أخطاء.</p>";

    echo "<h3>روابط للاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='test_permissions.php'>صفحة الاختبار الشاملة</a></li>";
    echo "<li><a href='setup_permissions.php'>صفحة الإعداد</a></li>";
    echo "<li><a href='manage_permissions.php'>إدارة الصلاحيات</a></li>";
    echo "<li><a href='accounts.php'>إدارة الحسابات</a></li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h2>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</h2>";
    echo "<p>تفاصيل الخطأ:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
} catch (Error $e) {
    echo "<h2>❌ خطأ فادح: " . htmlspecialchars($e->getMessage()) . "</h2>";
    echo "<p>الملف: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
