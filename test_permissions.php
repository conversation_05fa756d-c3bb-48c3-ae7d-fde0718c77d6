<?php
/**
 * ملف اختبار نظام الصلاحيات
 */

// بدء الجلسة
session_start();

// تضمين الملفات المطلوبة
include 'db_connection.php';
include 'encryption_functions.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// محاكاة تسجيل دخول مدير
if (!isset($_SESSION['account_id'])) {
    // البحث عن حساب مدير
    $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
    $admin_result = $conn->query($admin_query);
    
    if ($admin_result && $admin_result->num_rows > 0) {
        $admin = $admin_result->fetch_assoc();
        $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
        $_SESSION['username'] = 'admin_test';
        $_SESSION['role'] = 'admin';
    } else {
        die('لا يوجد حساب مدير نشط في النظام');
    }
}

// تضمين نظام الصلاحيات
include 'permissions_system.php';

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-success {
            color: #27ae60;
            font-weight: bold;
        }
        
        .test-error {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .test-info {
            color: #3498db;
            font-weight: bold;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .permission-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #3498db;
        }
        
        .permission-test.success {
            border-right-color: #27ae60;
        }
        
        .permission-test.error {
            border-right-color: #e74c3c;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: #3498db;
            color: white;
        }
        
        .status-active {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> اختبار نظام الصلاحيات</h1>
        
        <!-- اختبار الاتصال بقاعدة البيانات -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> اختبار قاعدة البيانات</h2>
            <?php
            try {
                $tables_check = [
                    'modules' => 'جدول الوحدات',
                    'permissions' => 'جدول الصلاحيات', 
                    'roles' => 'جدول الأدوار',
                    'role_permissions' => 'جدول صلاحيات الأدوار',
                    'user_permissions' => 'جدول صلاحيات المستخدمين',
                    'permission_logs' => 'جدول سجل الصلاحيات'
                ];
                
                echo "<div class='test-grid'>";
                foreach ($tables_check as $table => $description) {
                    $result = $conn->query("SHOW TABLES LIKE '$table'");
                    if ($result && $result->num_rows > 0) {
                        $count = $conn->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
                        echo "<div class='permission-test success'>";
                        echo "<i class='fas fa-check-circle'></i> $description";
                        echo "<br><small>$count سجل</small>";
                        echo "</div>";
                    } else {
                        echo "<div class='permission-test error'>";
                        echo "<i class='fas fa-times-circle'></i> $description";
                        echo "<br><small>غير موجود</small>";
                        echo "</div>";
                    }
                }
                echo "</div>";
            } catch (Exception $e) {
                echo "<p class='test-error'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- اختبار نظام الصلاحيات -->
        <div class="test-section">
            <h2><i class="fas fa-shield-alt"></i> اختبار نظام الصلاحيات</h2>
            <?php
            try {
                // اختبار إنشاء مثيل نظام الصلاحيات
                $permissions_system = new PermissionsSystem($conn, $key);
                echo "<p class='test-success'><i class='fas fa-check'></i> تم إنشاء نظام الصلاحيات بنجاح</p>";
                
                // اختبار فحص صلاحية المدير
                $is_admin = $permissions_system->isAdmin();
                if ($is_admin) {
                    echo "<p class='test-success'><i class='fas fa-check'></i> المستخدم الحالي مدير نظام</p>";
                } else {
                    echo "<p class='test-error'><i class='fas fa-times'></i> المستخدم الحالي ليس مدير نظام</p>";
                }
                
                // اختبار فحص صلاحيات محددة
                $test_permissions = [
                    ['accounts', 'view', 'عرض الحسابات'],
                    ['accounts', 'create', 'إنشاء حسابات'],
                    ['accounts', 'edit', 'تعديل الحسابات'],
                    ['accounts', 'delete', 'حذف الحسابات'],
                    ['accounts', 'manage_permissions', 'إدارة الصلاحيات']
                ];
                
                echo "<h3>اختبار الصلاحيات:</h3>";
                echo "<div class='test-grid'>";
                foreach ($test_permissions as $test) {
                    $has_permission = $permissions_system->hasPermission($test[0], $test[1]);
                    $class = $has_permission ? 'success' : 'error';
                    $icon = $has_permission ? 'check-circle' : 'times-circle';
                    
                    echo "<div class='permission-test $class'>";
                    echo "<i class='fas fa-$icon'></i> {$test[2]}";
                    echo "<br><small>{$test[0]} - {$test[1]}</small>";
                    echo "</div>";
                }
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p class='test-error'>خطأ في نظام الصلاحيات: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- عرض إحصائيات النظام -->
        <div class="test-section">
            <h2><i class="fas fa-chart-pie"></i> إحصائيات النظام</h2>
            <?php
            try {
                $stats = [
                    'الوحدات' => $conn->query("SELECT COUNT(*) as count FROM modules")->fetch_assoc()['count'],
                    'الصلاحيات' => $conn->query("SELECT COUNT(*) as count FROM permissions")->fetch_assoc()['count'],
                    'الأدوار' => $conn->query("SELECT COUNT(*) as count FROM roles")->fetch_assoc()['count'],
                    'صلاحيات الأدوار' => $conn->query("SELECT COUNT(*) as count FROM role_permissions")->fetch_assoc()['count'],
                    'صلاحيات المستخدمين' => $conn->query("SELECT COUNT(*) as count FROM user_permissions")->fetch_assoc()['count'],
                    'سجل الصلاحيات' => $conn->query("SELECT COUNT(*) as count FROM permission_logs")->fetch_assoc()['count']
                ];
                
                echo "<table>";
                echo "<tr><th>العنصر</th><th>العدد</th></tr>";
                foreach ($stats as $item => $count) {
                    echo "<tr><td>$item</td><td>$count</td></tr>";
                }
                echo "</table>";
                
            } catch (Exception $e) {
                echo "<p class='test-error'>خطأ في جلب الإحصائيات: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- عرض الأدوار والصلاحيات -->
        <div class="test-section">
            <h2><i class="fas fa-users-cog"></i> الأدوار المتاحة</h2>
            <?php
            try {
                $roles_query = "SELECT * FROM roles ORDER BY role_name";
                $roles_result = $conn->query($roles_query);
                
                if ($roles_result && $roles_result->num_rows > 0) {
                    echo "<table>";
                    echo "<tr><th>اسم الدور</th><th>الاسم بالعربية</th><th>الوصف</th><th>دور نظام</th><th>الحالة</th></tr>";
                    
                    while ($role = $roles_result->fetch_assoc()) {
                        $system_role = $role['is_system_role'] ? 'نعم' : 'لا';
                        $status = $role['is_active'] ? "<span class='status-active'>نشط</span>" : "<span class='status-inactive'>متوقف</span>";
                        
                        echo "<tr>";
                        echo "<td>{$role['role_name']}</td>";
                        echo "<td>{$role['role_name_ar']}</td>";
                        echo "<td>{$role['description']}</td>";
                        echo "<td>$system_role</td>";
                        echo "<td>$status</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='test-error'>لا توجد أدوار في النظام</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='test-error'>خطأ في جلب الأدوار: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> روابط مفيدة</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="setup_permissions.php" style="background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-cogs"></i> إعداد النظام
                </a>
                <a href="manage_permissions.php" style="background: #e74c3c; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                </a>
                <a href="accounts.php" style="background: #27ae60; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-users"></i> إدارة الحسابات
                </a>
                <a href="stores.php" style="background: #f39c12; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>
