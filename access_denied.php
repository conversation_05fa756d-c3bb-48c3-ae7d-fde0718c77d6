<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفض الوصول</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .access-denied-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .access-denied-icon {
            font-size: 80px;
            color: #e74c3c;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        .access-denied-title {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .access-denied-message {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .access-denied-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-right: 4px solid #e74c3c;
        }

        .access-denied-details h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .access-denied-details p {
            color: #6c757d;
            margin: 5px 0;
            font-size: 14px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        }

        .contact-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }

        .contact-info h5 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .contact-info p {
            color: #7f8c8d;
            font-size: 14px;
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .access-denied-container {
                padding: 30px 20px;
            }

            .access-denied-icon {
                font-size: 60px;
            }

            .access-denied-title {
                font-size: 24px;
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="access-denied-icon">
            <i class="fas fa-ban"></i>
        </div>
        
        <h1 class="access-denied-title">رفض الوصول</h1>
        
        <p class="access-denied-message">
            عذراً، ليس لديك الصلاحية اللازمة للوصول إلى هذه الصفحة أو تنفيذ هذا الإجراء.
        </p>

        <div class="access-denied-details">
            <h4><i class="fas fa-info-circle"></i> تفاصيل الخطأ</h4>
            <p><strong>كود الخطأ:</strong> 403 - Forbidden</p>
            <p><strong>الصفحة المطلوبة:</strong> <?php echo htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'غير محدد'); ?></p>
            <p><strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <?php if (isset($_SESSION['username'])): ?>
            <p><strong>المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></p>
            <?php endif; ?>
        </div>

        <div class="btn-group">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للخلف
            </a>
            
            <?php if (isset($_SESSION['role'])): ?>
                <?php if ($_SESSION['role'] === 'admin'): ?>
                    <a href="stores.php" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        الصفحة الرئيسية
                    </a>
                <?php elseif ($_SESSION['role'] === 'user'): ?>
                    <a href="users.php?account_id=<?php echo urlencode($_SESSION['account_id'] ?? ''); ?>" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        الصفحة الرئيسية
                    </a>
                <?php else: ?>
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        الصفحة الرئيسية
                    </a>
                <?php endif; ?>
            <?php else: ?>
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </a>
            <?php endif; ?>
        </div>

        <div class="contact-info">
            <h5><i class="fas fa-headset"></i> تحتاج مساعدة؟</h5>
            <p>إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بمدير النظام</p>
            <p>أو طلب الصلاحيات اللازمة من خلال قسم إدارة الحسابات</p>
        </div>
    </div>

    <script>
        // تسجيل محاولة الوصول غير المصرح بها
        console.warn('محاولة وصول غير مصرح بها إلى:', window.location.href);
        
        // إضافة تأثير بصري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.access-denied-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(-30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.5s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
