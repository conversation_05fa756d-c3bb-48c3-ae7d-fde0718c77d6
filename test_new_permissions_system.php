<?php
/**
 * اختبار نظام الصلاحيات الجديد المحسن
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين الملفات المطلوبة
include_once 'db_connection.php';
include_once 'encryption_functions.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// محاكاة تسجيل دخول مدير
if (!isset($_SESSION['account_id'])) {
    $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
    $admin_result = $conn->query($admin_query);
    
    if ($admin_result && $admin_result->num_rows > 0) {
        $admin = $admin_result->fetch_assoc();
        $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
        $_SESSION['username'] = 'admin_test';
        $_SESSION['role'] = 'admin';
    }
}

// تضمين نظام الصلاحيات
include_once 'permissions_system.php';
include_once 'auth_check.php';

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصلاحيات الجديد</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .module-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .module-card.accessible {
            border-right-color: #27ae60;
            background: linear-gradient(135deg, #d5f4e6 0%, #e8f8f0 100%);
        }
        
        .module-card.not-accessible {
            border-right-color: #e74c3c;
            background: linear-gradient(135deg, #fdeaea 0%, #fdf2f2 100%);
        }
        
        .permission-list {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
        
        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            font-size: 12px;
        }
        
        .status-yes {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-no {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .links-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .test-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
            <i class="fas fa-shield-alt"></i> نظام الصلاحيات الجديد المحسن
        </h1>
        
        <!-- اختبار الوحدات والصلاحيات -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-cogs"></i> اختبار الوحدات والصلاحيات
            </div>
            
            <?php
            try {
                // جلب الوحدات
                $modules_query = "SELECT * FROM modules ORDER BY sort_order, module_name_ar";
                $modules_result = $conn->query($modules_query);
                
                // جلب الصلاحيات
                $permissions_query = "SELECT * FROM permissions ORDER BY permission_name_ar";
                $permissions_result = $conn->query($permissions_query);
                $permissions = [];
                while ($perm = $permissions_result->fetch_assoc()) {
                    $permissions[] = $perm;
                }
                
                echo "<div class='modules-grid'>";
                
                while ($module = $modules_result->fetch_assoc()) {
                    $has_access = hasAnyPermission($module['module_name'], ['access']);
                    $card_class = $has_access ? 'accessible' : 'not-accessible';
                    $access_icon = $has_access ? 'check-circle' : 'times-circle';
                    $access_text = $has_access ? 'متاح' : 'غير متاح';
                    
                    echo "<div class='module-card $card_class'>";
                    echo "<div style='display: flex; align-items: center; margin-bottom: 10px;'>";
                    echo "<i class='{$module['icon_class']}' style='margin-left: 10px; font-size: 18px;'></i>";
                    echo "<strong>{$module['module_name_ar']}</strong>";
                    echo "<i class='fas fa-$access_icon' style='margin-right: auto; color: " . ($has_access ? '#27ae60' : '#e74c3c') . ";'></i>";
                    echo "</div>";
                    echo "<div style='color: #666; font-size: 12px; margin-bottom: 10px;'>{$module['description']}</div>";
                    echo "<div style='text-align: center; font-weight: bold; color: " . ($has_access ? '#27ae60' : '#e74c3c') . ";'>$access_text</div>";
                    
                    if ($has_access) {
                        echo "<div class='permission-list'>";
                        echo "<div style='font-weight: bold; margin-bottom: 5px; color: #333;'>العمليات المتاحة:</div>";
                        
                        foreach ($permissions as $permission) {
                            if ($permission['permission_name'] === 'access') continue;
                            
                            $has_permission = hasAnyPermission($module['module_name'], [$permission['permission_name']]);
                            $status_class = $has_permission ? 'status-yes' : 'status-no';
                            $status_text = $has_permission ? '✓' : '✗';
                            
                            echo "<div class='permission-item'>";
                            echo "<span>{$permission['permission_name_ar']}</span>";
                            echo "<span class='$status_class'>$status_text</span>";
                            echo "</div>";
                        }
                        
                        echo "</div>";
                    }
                    
                    echo "</div>";
                }
                
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>خطأ في جلب البيانات: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- روابط الاختبار -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-link"></i> روابط الاختبار
            </div>
            <div class="links-section">
                <a href="manage_permissions.php" class="test-link">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات الجديدة
                </a>
                <a href="purchase_invoices.php?store_id=<?php echo urlencode(encrypt(1, $key)); ?>" class="test-link">
                    <i class="fas fa-shopping-cart"></i> فواتير الشراء
                </a>
                <a href="test_organized_permissions.php" class="test-link">
                    <i class="fas fa-vial"></i> اختبار شامل
                </a>
                <a href="stores.php" class="test-link">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="test-section">
            <div class="section-header">
                <i class="fas fa-info-circle"></i> معلومات النظام الجديد
            </div>
            <div style="line-height: 1.8;">
                <h4 style="color: #3498db;">✨ المميزات الجديدة:</h4>
                <ul style="margin-right: 20px;">
                    <li><strong>لوحة التحكم المنظمة:</strong> عرض الوحدات بناءً على الـ sidebar الفعلي</li>
                    <li><strong>تفعيل تدريجي:</strong> تفعيل الوحدة أولاً ثم اختيار العمليات المسموحة</li>
                    <li><strong>إدارة الفروع:</strong> تحديد الفروع المسموحة لكل مستخدم</li>
                    <li><strong>واجهة تفاعلية:</strong> تبويبات منفصلة وتحديث فوري</li>
                    <li><strong>أمان محسن:</strong> فحص دقيق للصلاحيات في كل عملية</li>
                </ul>
                
                <h4 style="color: #e74c3c; margin-top: 20px;">🔧 كيفية الاستخدام:</h4>
                <ol style="margin-right: 20px;">
                    <li>اختر المستخدم من القائمة المنسدلة</li>
                    <li>في تبويب "صلاحيات الوحدات": فعل الوحدات المطلوبة</li>
                    <li>اختر العمليات المسموحة في كل وحدة مفعلة</li>
                    <li>في تبويب "صلاحيات الفروع": حدد الفروع المسموحة</li>
                    <li>احفظ التغييرات وجرب الوصول للصفحات</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
