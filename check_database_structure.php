<?php
include_once 'db_connection.php';

echo "<h2>🔍 فحص هيكل قاعدة البيانات الحالية</h2>";

// فحص الجداول الموجودة
echo "<h3>📊 الجداول الموجودة:</h3>";
$result = $conn->query("SHOW TABLES");
$existing_tables = [];

if ($result->num_rows > 0) {
    echo "<ul>";
    while($row = $result->fetch_array()) {
        $table_name = $row[0];
        $existing_tables[] = $table_name;
        echo "<li><strong>$table_name</strong></li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ لا توجد جداول في قاعدة البيانات</p>";
}

// فحص هيكل جدول accounts
if (in_array('accounts', $existing_tables)) {
    echo "<h3>👤 هيكل جدول accounts:</h3>";
    $result = $conn->query("DESCRIBE accounts");
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// فحص هيكل جدول stores
if (in_array('stores', $existing_tables)) {
    echo "<h3>🏪 هيكل جدول stores:</h3>";
    $result = $conn->query("DESCRIBE stores");
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// فحص الجداول المطلوبة لنظام الصلاحيات
$required_tables = ['roles', 'modules', 'permissions', 'role_permissions', 'user_permissions', 'user_stores', 'module_permissions', 'permission_logs'];

echo "<h3>🔧 حالة جداول نظام الصلاحيات:</h3>";
echo "<ul>";
foreach ($required_tables as $table) {
    if (in_array($table, $existing_tables)) {
        echo "<li>✅ <strong>$table</strong> - موجود</li>";
    } else {
        echo "<li>❌ <strong>$table</strong> - غير موجود</li>";
    }
}
echo "</ul>";

// فحص عمود access_type في جدول accounts
if (in_array('accounts', $existing_tables)) {
    echo "<h3>🔍 فحص عمود access_type في جدول accounts:</h3>";
    $result = $conn->query("SHOW COLUMNS FROM accounts LIKE 'access_type'");
    if ($result->num_rows > 0) {
        echo "<p>✅ عمود access_type موجود</p>";
        $row = $result->fetch_assoc();
        echo "<p>النوع: " . $row['Type'] . "</p>";
        echo "<p>القيمة الافتراضية: " . $row['Default'] . "</p>";
    } else {
        echo "<p>❌ عمود access_type غير موجود</p>";
    }
}

// فحص بيانات المستخدمين
if (in_array('accounts', $existing_tables)) {
    echo "<h3>👥 بيانات المستخدمين الحاليين:</h3>";
    $result = $conn->query("SELECT account_id, username, role, status FROM accounts LIMIT 10");
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الدور</th><th>الحالة</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['account_id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['role'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// فحص بيانات الفروع
if (in_array('stores', $existing_tables)) {
    echo "<h3>🏪 بيانات الفروع الحالية:</h3>";
    $result = $conn->query("SELECT store_id, store_name, status FROM stores LIMIT 10");
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم الفرع</th><th>الحالة</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['store_id'] . "</td>";
            echo "<td>" . $row['store_name'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

$conn->close();
?>
