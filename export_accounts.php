<?php
include 'auth_check.php';

// فحص صلاحية التصدير
checkPagePermission('accounts', 'export');

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

try {
    // إنشاء ملف Excel جديد
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // تعيين اتجاه النص من اليمين لليسار
    $sheet->setRightToLeft(true);
    
    // تعيين عنوان الورقة
    $sheet->setTitle('الحسابات');
    
    // إعداد العناوين
    $headers = [
        'A1' => 'رقم الحساب',
        'B1' => 'اسم المستخدم', 
        'C1' => 'الدور',
        'D1' => 'الاسم الكامل',
        'E1' => 'رقم الهاتف',
        'F1' => 'الفرع',
        'G1' => 'الحالة',
        'H1' => 'تاريخ الإنشاء'
    ];
    
    // كتابة العناوين
    foreach ($headers as $cell => $header) {
        $sheet->setCellValue($cell, $header);
    }
    
    // تنسيق العناوين
    $headerStyle = [
        'font' => [
            'bold' => true,
            'size' => 12,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => '3F51B5']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ];
    
    $sheet->getStyle('A1:H1')->applyFromArray($headerStyle);
    
    // جلب بيانات الحسابات
    $sql = "SELECT a.account_id, a.username, a.role, a.name, a.phone, a.status, 
                   s.name as store_name, a.created_at
            FROM accounts a 
            LEFT JOIN stores s ON a.store_id = s.store_id 
            WHERE a.status != 'requested'
            ORDER BY a.account_id";
    
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception('خطأ في استعلام قاعدة البيانات: ' . $conn->error);
    }
    
    $row = 2; // البدء من الصف الثاني
    
    while ($data = $result->fetch_assoc()) {
        // تحويل الدور إلى العربية
        $role_ar = '';
        switch ($data['role']) {
            case 'admin':
                $role_ar = 'مدير النظام';
                break;
            case 'store_manager':
                $role_ar = 'مدير فرع';
                break;
            case 'purchaser':
                $role_ar = 'مشتري';
                break;
            case 'user':
                $role_ar = 'مستخدم';
                break;
            case 'dealer':
                $role_ar = 'تاجر';
                break;
            default:
                $role_ar = $data['role'];
        }
        
        // تحويل الحالة إلى العربية
        $status_ar = $data['status'] === 'active' ? 'نشط' : 'متوقف';
        
        // تحديد الفرع
        $branch = $data['role'] === 'admin' ? 'مدير النظام' : ($data['store_name'] ?? 'غير محدد');
        
        // كتابة البيانات
        $sheet->setCellValue('A' . $row, $data['account_id']);
        $sheet->setCellValue('B' . $row, $data['username']);
        $sheet->setCellValue('C' . $row, $role_ar);
        $sheet->setCellValue('D' . $row, $data['name']);
        $sheet->setCellValue('E' . $row, $data['phone']);
        $sheet->setCellValue('F' . $row, $branch);
        $sheet->setCellValue('G' . $row, $status_ar);
        $sheet->setCellValue('H' . $row, $data['created_at'] ?? '');
        
        $row++;
    }
    
    // تنسيق البيانات
    $dataStyle = [
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                'color' => ['rgb' => 'CCCCCC']
            ]
        ]
    ];
    
    $sheet->getStyle('A1:H' . ($row - 1))->applyFromArray($dataStyle);
    
    // تعديل عرض الأعمدة
    $sheet->getColumnDimension('A')->setWidth(12);
    $sheet->getColumnDimension('B')->setWidth(20);
    $sheet->getColumnDimension('C')->setWidth(15);
    $sheet->getColumnDimension('D')->setWidth(25);
    $sheet->getColumnDimension('E')->setWidth(15);
    $sheet->getColumnDimension('F')->setWidth(20);
    $sheet->getColumnDimension('G')->setWidth(12);
    $sheet->getColumnDimension('H')->setWidth(20);
    
    // إضافة معلومات إضافية
    $infoRow = $row + 2;
    $sheet->setCellValue('A' . $infoRow, 'تاريخ التصدير:');
    $sheet->setCellValue('B' . $infoRow, date('Y-m-d H:i:s'));
    $sheet->setCellValue('A' . ($infoRow + 1), 'المستخدم:');
    $sheet->setCellValue('B' . ($infoRow + 1), $_SESSION['username'] ?? 'غير محدد');
    $sheet->setCellValue('A' . ($infoRow + 2), 'إجمالي الحسابات:');
    $sheet->setCellValue('B' . ($infoRow + 2), $row - 2);
    
    // تنسيق المعلومات الإضافية
    $infoStyle = [
        'font' => [
            'bold' => true,
            'size' => 10
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => 'F8F9FA']
        ]
    ];
    
    $sheet->getStyle('A' . $infoRow . ':B' . ($infoRow + 2))->applyFromArray($infoStyle);
    
    // تسجيل عملية التصدير
    $logged_in_account_id = decrypt($_SESSION['account_id'], $key);
    $log_sql = "INSERT INTO system_logs (account_id, action_type, table_name, description) 
                VALUES (?, 'export', 'accounts', ?)";
    $description = "تم تصدير بيانات الحسابات إلى ملف Excel";
    $log_stmt = $conn->prepare($log_sql);
    $log_stmt->bind_param("is", $logged_in_account_id, $description);
    $log_stmt->execute();
    $log_stmt->close();
    
    // إعداد الاستجابة للتحميل
    $filename = 'accounts_export_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، إعادة توجيه مع رسالة خطأ
    session_start();
    $_SESSION['message'] = 'حدث خطأ أثناء تصدير البيانات: ' . $e->getMessage();
    $_SESSION['message_type'] = 'error';
    header('Location: accounts.php');
    exit();
}

$conn->close();
?>
