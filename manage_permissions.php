<?php
include 'auth_check.php';

// التحقق من صلاحية إدارة الصلاحيات
// السماح للمدير بالوصول دائماً
if ($_SESSION['role'] !== 'admin') {
    checkPagePermission('accounts', 'manage');
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'grant_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
                $notes = $_POST['notes'] ?? null;

                try {
                    $result = $permissions_system->grantUserPermission($user_id, $module_name, $permission_name, $expires_at, $notes);
                    echo json_encode(['success' => $result, 'message' => 'تم منح الصلاحية بنجاح']);
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
                }
                break;

            case 'revoke_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $reason = $_POST['reason'] ?? null;

                try {
                    $result = $permissions_system->revokeUserPermission($user_id, $module_name, $permission_name, $reason);
                    echo json_encode(['success' => $result, 'message' => 'تم سحب الصلاحية بنجاح']);
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
                }
                break;

            case 'get_user_permissions':
                $user_id = (int)$_POST['user_id'];

                // الحصول على نوع الوصول للمستخدم
                $access_type_query = "SELECT access_type FROM accounts WHERE account_id = ?";
                $stmt = $conn->prepare($access_type_query);
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $user_access_type = 'cashier_system';
                if ($result->num_rows > 0) {
                    $row = $result->fetch_assoc();
                    $user_access_type = $row['access_type'] ?? 'cashier_system';
                }
                $stmt->close();

                $permissions = $permissions_system->getUserPermissions($user_id);

                // تصفية الصلاحيات حسب نوع الوصول
                if ($user_access_type === 'cashier_system') {
                    // عرض وحدات نظام الكاشير فقط
                    $cashier_modules = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];
                    $filtered_permissions = [];
                    foreach ($cashier_modules as $module) {
                        if (isset($permissions[$module])) {
                            $filtered_permissions[$module] = $permissions[$module];
                        }
                    }
                    $permissions = $filtered_permissions;
                }

                echo json_encode(['success' => true, 'permissions' => $permissions, 'access_type' => $user_access_type]);
                break;

            case 'grant_store_access':
                $user_id = (int)$_POST['user_id'];
                $store_id = (int)$_POST['store_id'];

                $stmt = $conn->prepare("INSERT IGNORE INTO user_stores (user_id, store_id, granted) VALUES (?, ?, TRUE)");
                $stmt->bind_param("ii", $user_id, $store_id);
                $result = $stmt->execute();
                $stmt->close();

                echo json_encode(['success' => $result, 'message' => 'تم منح الوصول للفرع بنجاح']);
                break;

            case 'revoke_store_access':
                $user_id = (int)$_POST['user_id'];
                $store_id = (int)$_POST['store_id'];

                $stmt = $conn->prepare("DELETE FROM user_stores WHERE user_id = ? AND store_id = ?");
                $stmt->bind_param("ii", $user_id, $store_id);
                $result = $stmt->execute();
                $stmt->close();

                echo json_encode(['success' => $result, 'message' => 'تم سحب الوصول للفرع بنجاح']);
                break;

            case 'get_user_stores':
                $user_id = (int)$_POST['user_id'];

                $stmt = $conn->prepare("SELECT store_id FROM user_stores WHERE user_id = ? AND granted = TRUE");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();

                $user_stores = [];
                while ($row = $result->fetch_assoc()) {
                    $user_stores[] = $row['store_id'];
                }
                $stmt->close();

                echo json_encode(['success' => true, 'stores' => $user_stores]);
                break;

            case 'change_access_type':
                $user_id = (int)$_POST['user_id'];
                $access_type = $_POST['access_type'];

                // التحقق من صحة نوع الوصول
                if (!in_array($access_type, ['admin_panel', 'cashier_system'])) {
                    echo json_encode(['success' => false, 'message' => 'نوع وصول غير صحيح']);
                    break;
                }

                $stmt = $conn->prepare("UPDATE accounts SET access_type = ? WHERE account_id = ?");
                $stmt->bind_param("si", $access_type, $user_id);
                $result = $stmt->execute();
                $stmt->close();

                $access_type_ar = $access_type == 'cashier_system' ? 'نظام الكاشير' : 'النظام الإداري';
                echo json_encode(['success' => $result, 'message' => "تم تغيير نوع الوصول إلى $access_type_ar بنجاح"]);
                break;

            case 'get_user_access_type':
                $user_id = (int)$_POST['user_id'];

                $stmt = $conn->prepare("SELECT access_type FROM accounts WHERE account_id = ?");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $row = $result->fetch_assoc();
                    echo json_encode(['success' => true, 'access_type' => $row['access_type']]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
                }
                $stmt->close();
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// جلب قائمة المستخدمين
$users_query = "SELECT account_id, username, name, role FROM accounts WHERE status = 'active' ORDER BY name";
$users_result = $conn->query($users_query);

// جلب قائمة الوحدات
$modules_query = "SELECT * FROM modules WHERE is_active = TRUE ORDER BY sort_order, module_name_ar";
$modules_result = $conn->query($modules_query);

// جلب قائمة الصلاحيات
$permissions_query = "SELECT * FROM permissions ORDER BY permission_name_ar";
$permissions_result = $conn->query($permissions_query);

// جلب قائمة الفروع
$stores_query = "SELECT store_id, name FROM stores ORDER BY name";
$stores_result = $conn->query($stores_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        .permissions-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .user-selector {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .module-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .module-header {
            background: linear-gradient(135deg, #3f51b5, #5c6bc0);
            color: white;
            padding: 15px;
            font-weight: bold;
        }

        .module-permissions {
            padding: 15px;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .permission-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .no-user-selected {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 18px;
        }

        .tabs-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            color: #3f51b5;
            border-bottom: 3px solid #3f51b5;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active:hover {
            background: white;
        }

        .stores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .store-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .store-card:hover {
            border-color: #3f51b5;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .store-card.selected {
            border-color: #4CAF50;
            background: #f8fff8;
        }

        .store-card .store-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #3f51b5;
        }

        .store-card.selected .store-icon {
            color: #4CAF50;
        }

        .module-toggle-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .module-toggle-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .module-toggle-card.module-enabled {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
        }

        .module-toggle-card.module-disabled {
            border-color: #f44336;
            background: linear-gradient(135deg, #fff8f8 0%, #ffeaea 100%);
        }

        .module-toggle-card .module-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .module-toggle-card.module-enabled .module-icon {
            color: #4CAF50;
        }

        .module-toggle-card.module-disabled .module-icon {
            color: #f44336;
        }

        .module-toggle-card .module-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .module-toggle-card .module-status {
            font-size: 12px;
            font-weight: bold;
        }

        .dashboard-section {
            margin-bottom: 30px;
        }

        .operations-section {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
        }

        .access-type-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .access-type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }

        .access-type-card.selected {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .access-type-card.selected::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #27ae60);
        }

        .access-type-icon {
            margin-bottom: 20px;
        }

        .access-type-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .access-type-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .access-type-features {
            text-align: right;
            margin-bottom: 20px;
        }

        .access-type-features div {
            padding: 8px 0;
            color: #555;
            font-size: 14px;
        }

        .access-type-features i {
            color: #4CAF50;
            margin-left: 8px;
            width: 16px;
        }

        .access-type-status {
            font-weight: bold;
            padding: 10px;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .access-type-card.selected .access-type-status {
            background: #e8f5e8;
            border-color: #4CAF50;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="container">
        <div class="permissions-container">
            <h2><i class="fas fa-user-shield"></i> إدارة الصلاحيات</h2>

            <div class="user-selector">
                <h3>اختيار المستخدم</h3>
                <select id="userSelect" class="input-field" style="width: 100%; max-width: 400px;">
                    <option value="">-- اختر مستخدم --</option>
                    <?php while ($user = $users_result->fetch_assoc()): ?>
                        <option value="<?php echo $user['account_id']; ?>" data-role="<?php echo $user['role']; ?>">
                            <?php echo htmlspecialchars($user['name'] . ' (' . $user['username'] . ')'); ?>
                            - <?php echo htmlspecialchars($user['role']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>

            <!-- تبويبات إدارة الصلاحيات -->
            <div class="tabs-container" id="tabsContainer" style="display: none;">
                <div class="tabs">
                    <button class="tab-button active" onclick="showTab('permissions')">
                        <i class="fas fa-key"></i> صلاحيات الوحدات
                    </button>
                    <button class="tab-button" onclick="showTab('stores')">
                        <i class="fas fa-store"></i> صلاحيات الفروع
                    </button>
                    <button class="tab-button" onclick="showTab('access_type')">
                        <i class="fas fa-desktop"></i> نوع الوصول
                    </button>
                </div>
            </div>

            <div id="permissionsContent">
                <div class="no-user-selected">
                    <i class="fas fa-user-plus" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                    <p>يرجى اختيار مستخدم لعرض وإدارة صلاحياته</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let currentUserId = null;
        let userPermissions = {};
        let userStores = [];
        let currentTab = 'permissions';
        let currentUserAccessType = 'cashier_system';

        document.getElementById('userSelect').addEventListener('change', function() {
            const userId = this.value;
            if (userId) {
                currentUserId = userId;
                document.getElementById('tabsContainer').style.display = 'block';
                loadUserData(userId);
            } else {
                currentUserId = null;
                document.getElementById('tabsContainer').style.display = 'none';
                showNoUserSelected();
            }
        });

        function showTab(tabName) {
            currentTab = tabName;

            // تحديث أزرار التبويبات
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // عرض المحتوى المناسب
            if (tabName === 'permissions') {
                renderPermissions();
            } else if (tabName === 'stores') {
                renderStores();
            } else if (tabName === 'access_type') {
                renderAccessType();
            }
        }

        function loadUserData(userId) {
            loadUserPermissions(userId);
            loadUserStores(userId);
            loadUserAccessType(userId);
        }

        function loadUserPermissions(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_permissions&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userPermissions = data.permissions;
                    currentUserAccessType = data.access_type || 'cashier_system';
                    if (currentTab === 'permissions') {
                        renderPermissions();
                    }
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحميل الصلاحيات', 'error');
            });
        }

        function loadUserStores(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_stores&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userStores = data.stores;
                    if (currentTab === 'stores') {
                        renderStores();
                    }
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحميل الفروع', 'error');
            });
        }

        function renderPermissions() {
            const allModules = <?php
                $modules_result->data_seek(0);
                $modules_array = [];
                while ($module = $modules_result->fetch_assoc()) {
                    $modules_array[] = $module;
                }
                echo json_encode($modules_array);
            ?>;

            const permissions = <?php
                $permissions_result->data_seek(0);
                $permissions_array = [];
                while ($permission = $permissions_result->fetch_assoc()) {
                    $permissions_array[] = $permission;
                }
                echo json_encode($permissions_array);
            ?>;

            // تصفية الوحدات حسب نوع الوصول
            let modules = allModules;
            let systemTitle = 'النظام الإداري';
            let systemIcon = 'fas fa-cogs';
            let systemColor = '#3498db';

            if (currentUserAccessType === 'cashier_system') {
                // عرض وحدات نظام الكاشير فقط
                const cashierModuleNames = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];
                modules = allModules.filter(module => cashierModuleNames.includes(module.module_name));
                systemTitle = 'نظام الكاشير';
                systemIcon = 'fas fa-cash-register';
                systemColor = '#27ae60';
            }

            let html = '<div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += `<h3><i class="${systemIcon}"></i> إدارة صلاحيات ${systemTitle}</h3>`;
            html += `<p style="color: #666; margin-bottom: 20px;">اختر الوحدات والعمليات المسموحة للمستخدم في ${systemTitle}</p>`;

            // قسم لوحة التحكم - الوحدات الرئيسية
            html += '<div class="dashboard-section">';
            html += `<h4 style="color: ${systemColor}; margin-bottom: 15px; border-bottom: 2px solid ${systemColor}; padding-bottom: 5px;"><i class="${systemIcon}"></i> ${systemTitle} - الوحدات المتاحة</h4>`;
            html += '<div class="modules-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">';

            modules.forEach(module => {
                const hasAccess = userPermissions[module.module_name] && userPermissions[module.module_name]['access'];
                const moduleClass = hasAccess ? 'module-enabled' : 'module-disabled';

                html += `
                    <div class="module-toggle-card ${moduleClass}" onclick="toggleModuleAccess('${module.module_name}', !${hasAccess})">
                        <div class="module-icon">
                            <i class="${module.icon_class || 'fas fa-cog'}"></i>
                        </div>
                        <div class="module-name">${module.module_name_ar}</div>
                        <div class="module-status">
                            ${hasAccess ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i> مفعل' : '<i class="fas fa-times-circle" style="color: #f44336;"></i> معطل'}
                        </div>
                    </div>
                `;
            });

            html += '</div></div>';

            // قسم العمليات المتاحة للوحدات المفعلة
            const enabledModules = modules.filter(module =>
                userPermissions[module.module_name] && userPermissions[module.module_name]['access']
            );

            if (enabledModules.length > 0) {
                html += '<div class="operations-section">';
                html += '<h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;"><i class="fas fa-cogs"></i> العمليات المتاحة في الوحدات المفعلة</h4>';

                enabledModules.forEach(module => {
                    html += `
                        <div class="module-operations" style="background: #f8f9fa; border-radius: 8px; padding: 15px; margin-bottom: 15px; border-left: 4px solid #4CAF50;">
                            <h5 style="color: #333; margin-bottom: 10px;">
                                <i class="${module.icon_class || 'fas fa-cog'}"></i> ${module.module_name_ar}
                            </h5>
                            <div class="operations-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                    `;

                    // تصفية الصلاحيات حسب نوع النظام
                    let filteredPermissions = permissions;
                    if (currentUserAccessType === 'cashier_system') {
                        // صلاحيات نظام الكاشير فقط
                        const cashierPermissions = ['access', 'view', 'create_purchase', 'create_sale', 'close_shift', 'edit_profile', 'switch_store'];
                        filteredPermissions = permissions.filter(permission =>
                            cashierPermissions.includes(permission.permission_name)
                        );
                    }

                    filteredPermissions.forEach(permission => {
                        if (permission.permission_name === 'access') return; // تخطي صلاحية الوصول لأنها مفعلة بالفعل

                        const hasPermission = userPermissions[module.module_name] &&
                                            userPermissions[module.module_name][permission.permission_name];

                        html += `
                            <div class="operation-item" style="display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; background: white; border-radius: 6px; border: 1px solid #ddd;">
                                <span style="font-size: 14px;">${permission.permission_name_ar}</span>
                                <label class="permission-toggle" style="transform: scale(0.8);">
                                    <input type="checkbox"
                                           ${hasPermission ? 'checked' : ''}
                                           onchange="togglePermission('${module.module_name}', '${permission.permission_name}', this.checked)">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        `;
                    });

                    html += '</div></div>';
                });

                html += '</div>';
            } else {
                html += '<div style="text-align: center; padding: 40px; color: #999;">';
                html += '<i class="fas fa-info-circle" style="font-size: 48px; margin-bottom: 15px;"></i>';
                html += `<p>لم يتم تفعيل أي وحدة في ${systemTitle} بعد. يرجى تفعيل الوحدات المطلوبة أولاً.</p>`;
                html += '</div>';
            }

            // إضافة معلومات خاصة بنظام الكاشير
            if (currentUserAccessType === 'cashier_system') {
                html += '<div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-top: 20px; border-right: 4px solid #27ae60;">';
                html += '<h4 style="color: #27ae60; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> معلومات نظام الكاشير:</h4>';
                html += '<ul style="margin-right: 20px; line-height: 1.8; color: #2e7d32;">';
                html += '<li><strong>الرئيسية:</strong> الوصول للصفحة الرئيسية لنظام الكاشير</li>';
                html += '<li><strong>إضافة الفواتير:</strong> إنشاء فواتير الشراء والبيع بالجملة</li>';
                html += '<li><strong>تقفيل الوردية:</strong> تقفيل وردية الكاشير اليومية</li>';
                html += '<li><strong>إدارة الحساب:</strong> تعديل البيانات الشخصية والتبديل بين الفروع</li>';
                html += '</ul>';
                html += '</div>';
            }

            html += '</div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        function renderStores() {
            const stores = <?php
                $stores_result->data_seek(0);
                $stores_array = [];
                while ($store = $stores_result->fetch_assoc()) {
                    $stores_array[] = $store;
                }
                echo json_encode($stores_array);
            ?>;

            let html = '<div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += '<h3><i class="fas fa-store"></i> إدارة صلاحيات الفروع</h3>';
            html += '<p style="color: #666; margin-bottom: 20px;">اختر الفروع التي يمكن للمستخدم الوصول إليها</p>';
            html += '<div class="stores-grid">';

            stores.forEach(store => {
                const hasAccess = userStores.includes(parseInt(store.store_id));
                const selectedClass = hasAccess ? 'selected' : '';

                html += `
                    <div class="store-card ${selectedClass}" onclick="toggleStoreAccess(${store.store_id}, !${hasAccess})">
                        <div class="store-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div style="font-weight: bold; margin-bottom: 5px;">${store.name}</div>
                        <div style="color: #666; font-size: 12px;">
                            ${hasAccess ? 'مسموح' : 'غير مسموح'}
                        </div>
                    </div>
                `;
            });

            html += '</div></div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        let userAccessType = 'cashier_system';

        function loadUserAccessType(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_access_type&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userAccessType = data.access_type || 'cashier_system';
                    if (currentTab === 'access_type') {
                        renderAccessType();
                    }
                } else {
                    console.error('فشل في تحميل نوع الوصول:', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        function renderAccessType() {
            let html = '<div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += '<h3><i class="fas fa-desktop"></i> إدارة نوع الوصول للمستخدم</h3>';
            html += '<p style="color: #666; margin-bottom: 20px;">حدد نوع النظام الذي سيصل إليه المستخدم عند تسجيل الدخول</p>';

            html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">';

            // خيار النظام الإداري
            const isAdminPanel = userAccessType === 'admin_panel';
            const adminClass = isAdminPanel ? 'selected' : '';

            html += `
                <div class="access-type-card ${adminClass}" onclick="changeAccessType('admin_panel')">
                    <div class="access-type-icon">
                        <i class="fas fa-cogs" style="font-size: 48px; color: #3498db;"></i>
                    </div>
                    <div class="access-type-title">النظام الإداري</div>
                    <div class="access-type-description">
                        الوصول للوحة التحكم الإدارية مع جميع الوحدات والصلاحيات المخصصة للمستخدم
                    </div>
                    <div class="access-type-features">
                        <div><i class="fas fa-check"></i> إدارة الفواتير والأصناف</div>
                        <div><i class="fas fa-check"></i> التقارير والإحصائيات</div>
                        <div><i class="fas fa-check"></i> إدارة المخزون</div>
                    </div>
                    <div class="access-type-status">
                        ${isAdminPanel ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i> مفعل حالياً' : '<i class="fas fa-circle" style="color: #ccc;"></i> غير مفعل'}
                    </div>
                </div>
            `;

            // خيار نظام الكاشير
            const isCashierSystem = userAccessType === 'cashier_system';
            const cashierClass = isCashierSystem ? 'selected' : '';

            html += `
                <div class="access-type-card ${cashierClass}" onclick="changeAccessType('cashier_system')">
                    <div class="access-type-icon">
                        <i class="fas fa-cash-register" style="font-size: 48px; color: #27ae60;"></i>
                    </div>
                    <div class="access-type-title">نظام الكاشير</div>
                    <div class="access-type-description">
                        واجهة مبسطة للكاشير مع التركيز على عمليات البيع والمبيعات اليومية
                    </div>
                    <div class="access-type-features">
                        <div><i class="fas fa-check"></i> واجهة سهلة الاستخدام</div>
                        <div><i class="fas fa-check"></i> عمليات البيع السريعة</div>
                        <div><i class="fas fa-check"></i> إدارة المبيعات اليومية</div>
                    </div>
                    <div class="access-type-status">
                        ${isCashierSystem ? '<i class="fas fa-check-circle" style="color: #4CAF50;"></i> مفعل حالياً' : '<i class="fas fa-circle" style="color: #ccc;"></i> غير مفعل'}
                    </div>
                </div>
            `;

            html += '</div>';

            // معلومات إضافية
            html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; border-right: 4px solid #3498db;">';
            html += '<h4 style="color: #3498db; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> ملاحظات مهمة:</h4>';
            html += '<ul style="margin-right: 20px; line-height: 1.8;">';
            html += '<li><strong>النظام الإداري:</strong> مناسب للمديرين والموظفين الإداريين</li>';
            html += '<li><strong>نظام الكاشير:</strong> مناسب للكاشيرين والمستخدمين العاديين</li>';
            html += '<li><strong>تغيير النوع:</strong> يتطلب إعادة تسجيل دخول المستخدم ليصبح ساري المفعول</li>';
            html += '<li><strong>الصلاحيات:</strong> تطبق حسب نوع النظام المختار</li>';
            html += '</ul>';
            html += '</div>';

            html += '</div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        function changeAccessType(accessType) {
            if (!currentUserId) return;

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=change_access_type&user_id=${currentUserId}&access_type=${accessType}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userAccessType = accessType;
                    currentUserAccessType = accessType;

                    // إعادة تحميل الصلاحيات لعرض الوحدات المناسبة
                    loadUserPermissions(currentUserId);

                    renderAccessType();

                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 2000
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تغيير نوع الوصول', 'error');
            });
        }

        function togglePermission(moduleName, permissionName, granted) {
            if (!currentUserId) return;

            const action = granted ? 'grant_permission' : 'revoke_permission';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&module_name=${moduleName}&permission_name=${permissionName}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (!userPermissions[moduleName]) {
                        userPermissions[moduleName] = {};
                    }
                    userPermissions[moduleName][permissionName] = granted;

                    // إعادة عرض الصلاحيات إذا كانت صلاحية الوصول
                    if (permissionName === 'access') {
                        renderPermissions();
                    }

                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                    // إعادة تعيين الحالة السابقة
                    loadUserPermissions(currentUserId);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث الصلاحية', 'error');
                // إعادة تعيين الحالة السابقة
                loadUserPermissions(currentUserId);
            });
        }

        function toggleModuleAccess(moduleName, granted) {
            if (!currentUserId) return;

            // تبديل صلاحية الوصول للوحدة
            togglePermission(moduleName, 'access', granted);
        }

        function toggleStoreAccess(storeId, granted) {
            if (!currentUserId) return;

            const action = granted ? 'grant_store_access' : 'revoke_store_access';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&store_id=${storeId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (granted) {
                        if (!userStores.includes(storeId)) {
                            userStores.push(storeId);
                        }
                    } else {
                        userStores = userStores.filter(id => id !== storeId);
                    }

                    // إعادة عرض الفروع
                    renderStores();

                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث صلاحية الفرع', 'error');
            });
        }

        function showNoUserSelected() {
            document.getElementById('permissionsContent').innerHTML = `
                <div class="no-user-selected">
                    <i class="fas fa-user-plus" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                    <p>يرجى اختيار مستخدم لعرض وإدارة صلاحياته</p>
                </div>
            `;
        }
    </script>
</body>
</html>
