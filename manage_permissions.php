<?php
include 'auth_check.php';

// التحقق من صلاحية إدارة الصلاحيات
checkPagePermission('accounts', 'manage_permissions');

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'grant_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
                $notes = $_POST['notes'] ?? null;
                
                $result = $permissions_system->grantUserPermission($user_id, $module_name, $permission_name, $expires_at, $notes);
                echo json_encode(['success' => $result, 'message' => 'تم منح الصلاحية بنجاح']);
                break;
                
            case 'revoke_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $reason = $_POST['reason'] ?? null;
                
                $result = $permissions_system->revokeUserPermission($user_id, $module_name, $permission_name, $reason);
                echo json_encode(['success' => $result, 'message' => 'تم سحب الصلاحية بنجاح']);
                break;
                
            case 'get_user_permissions':
                $user_id = (int)$_POST['user_id'];
                $permissions = $permissions_system->getUserPermissions($user_id);
                echo json_encode(['success' => true, 'permissions' => $permissions]);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// جلب قائمة المستخدمين
$users_query = "SELECT account_id, username, name, role FROM accounts WHERE status = 'active' ORDER BY name";
$users_result = $conn->query($users_query);

// جلب قائمة الوحدات
$modules_query = "SELECT * FROM modules WHERE is_active = TRUE ORDER BY sort_order, module_name_ar";
$modules_result = $conn->query($modules_query);

// جلب قائمة الصلاحيات
$permissions_query = "SELECT * FROM permissions ORDER BY permission_name_ar";
$permissions_result = $conn->query($permissions_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        .permissions-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .user-selector {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .module-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .module-header {
            background: linear-gradient(135deg, #3f51b5, #5c6bc0);
            color: white;
            padding: 15px;
            font-weight: bold;
        }
        
        .module-permissions {
            padding: 15px;
        }
        
        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .permission-item:last-child {
            border-bottom: none;
        }
        
        .permission-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .permission-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #4CAF50;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .no-user-selected {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    
    <div class="container">
        <div class="permissions-container">
            <h2><i class="fas fa-user-shield"></i> إدارة الصلاحيات</h2>
            
            <div class="user-selector">
                <h3>اختيار المستخدم</h3>
                <select id="userSelect" class="input-field" style="width: 100%; max-width: 400px;">
                    <option value="">-- اختر مستخدم --</option>
                    <?php while ($user = $users_result->fetch_assoc()): ?>
                        <option value="<?php echo $user['account_id']; ?>" data-role="<?php echo $user['role']; ?>">
                            <?php echo htmlspecialchars($user['name'] . ' (' . $user['username'] . ')'); ?>
                            - <?php echo htmlspecialchars($user['role']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            
            <div id="permissionsContent">
                <div class="no-user-selected">
                    <i class="fas fa-user-plus" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                    <p>يرجى اختيار مستخدم لعرض وإدارة صلاحياته</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let currentUserId = null;
        let userPermissions = {};
        
        document.getElementById('userSelect').addEventListener('change', function() {
            const userId = this.value;
            if (userId) {
                currentUserId = userId;
                loadUserPermissions(userId);
            } else {
                currentUserId = null;
                showNoUserSelected();
            }
        });
        
        function loadUserPermissions(userId) {
            const content = document.getElementById('permissionsContent');
            content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الصلاحيات...</div>';
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_permissions&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userPermissions = data.permissions;
                    renderPermissions();
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحميل الصلاحيات', 'error');
            });
        }
        
        function renderPermissions() {
            const modules = <?php 
                $modules_result->data_seek(0);
                $modules_array = [];
                while ($module = $modules_result->fetch_assoc()) {
                    $modules_array[] = $module;
                }
                echo json_encode($modules_array);
            ?>;
            
            const permissions = <?php 
                $permissions_result->data_seek(0);
                $permissions_array = [];
                while ($permission = $permissions_result->fetch_assoc()) {
                    $permissions_array[] = $permission;
                }
                echo json_encode($permissions_array);
            ?>;
            
            let html = '<div class="permissions-grid">';
            
            modules.forEach(module => {
                html += `
                    <div class="module-card">
                        <div class="module-header">
                            <i class="${module.icon_class || 'fas fa-cog'}"></i>
                            ${module.module_name_ar}
                        </div>
                        <div class="module-permissions">
                `;
                
                permissions.forEach(permission => {
                    const hasPermission = userPermissions[module.module_name] && 
                                        userPermissions[module.module_name][permission.permission_name];
                    
                    html += `
                        <div class="permission-item">
                            <span>${permission.permission_name_ar}</span>
                            <label class="permission-toggle">
                                <input type="checkbox" 
                                       ${hasPermission ? 'checked' : ''}
                                       onchange="togglePermission('${module.module_name}', '${permission.permission_name}', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            document.getElementById('permissionsContent').innerHTML = html;
        }
        
        function togglePermission(moduleName, permissionName, granted) {
            if (!currentUserId) return;
            
            const action = granted ? 'grant_permission' : 'revoke_permission';
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&module_name=${moduleName}&permission_name=${permissionName}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (!userPermissions[moduleName]) {
                        userPermissions[moduleName] = {};
                    }
                    userPermissions[moduleName][permissionName] = granted;
                    
                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                    // إعادة تعيين الحالة السابقة
                    loadUserPermissions(currentUserId);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث الصلاحية', 'error');
                // إعادة تعيين الحالة السابقة
                loadUserPermissions(currentUserId);
            });
        }
        
        function showNoUserSelected() {
            document.getElementById('permissionsContent').innerHTML = `
                <div class="no-user-selected">
                    <i class="fas fa-user-plus" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                    <p>يرجى اختيار مستخدم لعرض وإدارة صلاحياته</p>
                </div>
            `;
        }
    </script>
</body>
</html>
