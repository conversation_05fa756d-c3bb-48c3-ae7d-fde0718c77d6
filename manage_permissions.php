<?php
include 'auth_check.php';

// التحقق من صلاحية إدارة الصلاحيات
checkPagePermission('accounts', 'manage');

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'grant_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
                $notes = $_POST['notes'] ?? null;

                $result = $permissions_system->grantUserPermission($user_id, $module_name, $permission_name, $expires_at, $notes);
                echo json_encode(['success' => $result, 'message' => 'تم منح الصلاحية بنجاح']);
                break;

            case 'revoke_permission':
                $user_id = (int)$_POST['user_id'];
                $module_name = $_POST['module_name'];
                $permission_name = $_POST['permission_name'];
                $reason = $_POST['reason'] ?? null;

                $result = $permissions_system->revokeUserPermission($user_id, $module_name, $permission_name, $reason);
                echo json_encode(['success' => $result, 'message' => 'تم سحب الصلاحية بنجاح']);
                break;

            case 'get_user_permissions':
                $user_id = (int)$_POST['user_id'];
                $permissions = $permissions_system->getUserPermissions($user_id);
                echo json_encode(['success' => true, 'permissions' => $permissions]);
                break;

            case 'grant_store_access':
                $user_id = (int)$_POST['user_id'];
                $store_id = (int)$_POST['store_id'];

                $stmt = $conn->prepare("INSERT IGNORE INTO user_stores (user_id, store_id, granted) VALUES (?, ?, TRUE)");
                $stmt->bind_param("ii", $user_id, $store_id);
                $result = $stmt->execute();
                $stmt->close();

                echo json_encode(['success' => $result, 'message' => 'تم منح الوصول للفرع بنجاح']);
                break;

            case 'revoke_store_access':
                $user_id = (int)$_POST['user_id'];
                $store_id = (int)$_POST['store_id'];

                $stmt = $conn->prepare("DELETE FROM user_stores WHERE user_id = ? AND store_id = ?");
                $stmt->bind_param("ii", $user_id, $store_id);
                $result = $stmt->execute();
                $stmt->close();

                echo json_encode(['success' => $result, 'message' => 'تم سحب الوصول للفرع بنجاح']);
                break;

            case 'get_user_stores':
                $user_id = (int)$_POST['user_id'];

                $stmt = $conn->prepare("SELECT store_id FROM user_stores WHERE user_id = ? AND granted = TRUE");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();

                $user_stores = [];
                while ($row = $result->fetch_assoc()) {
                    $user_stores[] = $row['store_id'];
                }
                $stmt->close();

                echo json_encode(['success' => true, 'stores' => $user_stores]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// جلب قائمة المستخدمين
$users_query = "SELECT account_id, username, name, role FROM accounts WHERE status = 'active' ORDER BY name";
$users_result = $conn->query($users_query);

// جلب قائمة الوحدات
$modules_query = "SELECT * FROM modules WHERE is_active = TRUE ORDER BY sort_order, module_name_ar";
$modules_result = $conn->query($modules_query);

// جلب قائمة الصلاحيات
$permissions_query = "SELECT * FROM permissions ORDER BY permission_name_ar";
$permissions_result = $conn->query($permissions_query);

// جلب قائمة الفروع
$stores_query = "SELECT store_id, name FROM stores ORDER BY name";
$stores_result = $conn->query($stores_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        .permissions-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .user-selector {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .module-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .module-header {
            background: linear-gradient(135deg, #3f51b5, #5c6bc0);
            color: white;
            padding: 15px;
            font-weight: bold;
        }

        .module-permissions {
            padding: 15px;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .permission-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .no-user-selected {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 18px;
        }

        .tabs-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            color: #3f51b5;
            border-bottom: 3px solid #3f51b5;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-button.active:hover {
            background: white;
        }

        .stores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .store-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .store-card:hover {
            border-color: #3f51b5;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .store-card.selected {
            border-color: #4CAF50;
            background: #f8fff8;
        }

        .store-card .store-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #3f51b5;
        }

        .store-card.selected .store-icon {
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <div class="container">
        <div class="permissions-container">
            <h2><i class="fas fa-user-shield"></i> إدارة الصلاحيات</h2>

            <div class="user-selector">
                <h3>اختيار المستخدم</h3>
                <select id="userSelect" class="input-field" style="width: 100%; max-width: 400px;">
                    <option value="">-- اختر مستخدم --</option>
                    <?php while ($user = $users_result->fetch_assoc()): ?>
                        <option value="<?php echo $user['account_id']; ?>" data-role="<?php echo $user['role']; ?>">
                            <?php echo htmlspecialchars($user['name'] . ' (' . $user['username'] . ')'); ?>
                            - <?php echo htmlspecialchars($user['role']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>

            <!-- تبويبات إدارة الصلاحيات -->
            <div class="tabs-container" id="tabsContainer" style="display: none;">
                <div class="tabs">
                    <button class="tab-button active" onclick="showTab('permissions')">
                        <i class="fas fa-key"></i> صلاحيات الوحدات
                    </button>
                    <button class="tab-button" onclick="showTab('stores')">
                        <i class="fas fa-store"></i> صلاحيات الفروع
                    </button>
                </div>
            </div>

            <div id="permissionsContent">
                <div class="no-user-selected">
                    <i class="fas fa-user-plus" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                    <p>يرجى اختيار مستخدم لعرض وإدارة صلاحياته</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let currentUserId = null;
        let userPermissions = {};
        let userStores = [];
        let currentTab = 'permissions';

        document.getElementById('userSelect').addEventListener('change', function() {
            const userId = this.value;
            if (userId) {
                currentUserId = userId;
                document.getElementById('tabsContainer').style.display = 'block';
                loadUserData(userId);
            } else {
                currentUserId = null;
                document.getElementById('tabsContainer').style.display = 'none';
                showNoUserSelected();
            }
        });

        function showTab(tabName) {
            currentTab = tabName;

            // تحديث أزرار التبويبات
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // عرض المحتوى المناسب
            if (tabName === 'permissions') {
                renderPermissions();
            } else if (tabName === 'stores') {
                renderStores();
            }
        }

        function loadUserData(userId) {
            loadUserPermissions(userId);
            loadUserStores(userId);
        }

        function loadUserPermissions(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_permissions&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userPermissions = data.permissions;
                    if (currentTab === 'permissions') {
                        renderPermissions();
                    }
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحميل الصلاحيات', 'error');
            });
        }

        function loadUserStores(userId) {
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_stores&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    userStores = data.stores;
                    if (currentTab === 'stores') {
                        renderStores();
                    }
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحميل الفروع', 'error');
            });
        }

        function renderPermissions() {
            const modules = <?php
                $modules_result->data_seek(0);
                $modules_array = [];
                while ($module = $modules_result->fetch_assoc()) {
                    $modules_array[] = $module;
                }
                echo json_encode($modules_array);
            ?>;

            const permissions = <?php
                $permissions_result->data_seek(0);
                $permissions_array = [];
                while ($permission = $permissions_result->fetch_assoc()) {
                    $permissions_array[] = $permission;
                }
                echo json_encode($permissions_array);
            ?>;

            let html = '<div class="permissions-grid">';

            modules.forEach(module => {
                html += `
                    <div class="module-card">
                        <div class="module-header">
                            <i class="${module.icon_class || 'fas fa-cog'}"></i>
                            ${module.module_name_ar}
                        </div>
                        <div class="module-permissions">
                `;

                permissions.forEach(permission => {
                    const hasPermission = userPermissions[module.module_name] &&
                                        userPermissions[module.module_name][permission.permission_name];

                    html += `
                        <div class="permission-item">
                            <span>${permission.permission_name_ar}</span>
                            <label class="permission-toggle">
                                <input type="checkbox"
                                       ${hasPermission ? 'checked' : ''}
                                       onchange="togglePermission('${module.module_name}', '${permission.permission_name}', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        function renderStores() {
            const stores = <?php
                $stores_result->data_seek(0);
                $stores_array = [];
                while ($store = $stores_result->fetch_assoc()) {
                    $stores_array[] = $store;
                }
                echo json_encode($stores_array);
            ?>;

            let html = '<div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">';
            html += '<h3><i class="fas fa-store"></i> إدارة صلاحيات الفروع</h3>';
            html += '<p style="color: #666; margin-bottom: 20px;">اختر الفروع التي يمكن للمستخدم الوصول إليها</p>';
            html += '<div class="stores-grid">';

            stores.forEach(store => {
                const hasAccess = userStores.includes(parseInt(store.store_id));
                const selectedClass = hasAccess ? 'selected' : '';

                html += `
                    <div class="store-card ${selectedClass}" onclick="toggleStoreAccess(${store.store_id}, !${hasAccess})">
                        <div class="store-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div style="font-weight: bold; margin-bottom: 5px;">${store.name}</div>
                        <div style="color: #666; font-size: 12px;">
                            ${hasAccess ? 'مسموح' : 'غير مسموح'}
                        </div>
                    </div>
                `;
            });

            html += '</div></div>';

            document.getElementById('permissionsContent').innerHTML = html;
        }

        function togglePermission(moduleName, permissionName, granted) {
            if (!currentUserId) return;

            const action = granted ? 'grant_permission' : 'revoke_permission';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&module_name=${moduleName}&permission_name=${permissionName}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (!userPermissions[moduleName]) {
                        userPermissions[moduleName] = {};
                    }
                    userPermissions[moduleName][permissionName] = granted;

                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                    // إعادة تعيين الحالة السابقة
                    loadUserPermissions(currentUserId);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث الصلاحية', 'error');
                // إعادة تعيين الحالة السابقة
                loadUserPermissions(currentUserId);
            });
        }

        function toggleStoreAccess(storeId, granted) {
            if (!currentUserId) return;

            const action = granted ? 'grant_store_access' : 'revoke_store_access';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}&user_id=${currentUserId}&store_id=${storeId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الكاش المحلي
                    if (granted) {
                        if (!userStores.includes(storeId)) {
                            userStores.push(storeId);
                        }
                    } else {
                        userStores = userStores.filter(id => id !== storeId);
                    }

                    // إعادة عرض الفروع
                    renderStores();

                    Swal.fire({
                        icon: 'success',
                        title: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('خطأ', 'حدث خطأ أثناء تحديث صلاحية الفرع', 'error');
            });
        }

        function showNoUserSelected() {
            document.getElementById('permissionsContent').innerHTML = `
                <div class="no-user-selected">
                    <i class="fas fa-user-plus" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                    <p>يرجى اختيار مستخدم لعرض وإدارة صلاحياته</p>
                </div>
            `;
        }
    </script>
</body>
</html>
