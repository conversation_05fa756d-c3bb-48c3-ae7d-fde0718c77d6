<?php
/**
 * ملف اختبار لتشخيص مشاكل التكوين
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تشخيص مشاكل التكوين</h1>";

try {
    // تضمين الملفات المطلوبة
    include_once 'db_connection.php';
    echo "<p style='color: green;'>✅ تم تحميل اتصال قاعدة البيانات</p>";
    
    // فحص الاتصال
    if ($conn->connect_error) {
        die("<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات: " . $conn->connect_error . "</p>");
    }
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
    
    // فحص جدول الحسابات
    $check_accounts = "SHOW TABLES LIKE 'accounts'";
    $result = $conn->query($check_accounts);
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ جدول الحسابات موجود</p>";
        
        // فحص أعمدة جدول الحسابات
        $check_columns = "SHOW COLUMNS FROM accounts";
        $columns_result = $conn->query($check_columns);
        if ($columns_result) {
            echo "<h3>أعمدة جدول الحسابات:</h3>";
            echo "<ul>";
            while ($column = $columns_result->fetch_assoc()) {
                echo "<li><strong>{$column['Field']}</strong> - {$column['Type']} - {$column['Default']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>❌ جدول الحسابات غير موجود</p>";
    }
    
    // اختبار إنشاء جدول بسيط
    echo "<h3>اختبار إنشاء جدول:</h3>";
    $test_table = "CREATE TABLE IF NOT EXISTS test_permissions_setup (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_field VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($test_table)) {
        echo "<p style='color: green;'>✅ تم إنشاء جدول الاختبار بنجاح</p>";
        
        // حذف جدول الاختبار
        $conn->query("DROP TABLE test_permissions_setup");
        echo "<p style='color: green;'>✅ تم حذف جدول الاختبار</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء جدول الاختبار: " . $conn->error . "</p>";
    }
    
    // اختبار إضافة عمود
    echo "<h3>اختبار إضافة عمود:</h3>";
    
    // فحص وجود عمود access_type
    $check_access_type = "SHOW COLUMNS FROM accounts LIKE 'access_type'";
    $access_result = $conn->query($check_access_type);
    
    if ($access_result && $access_result->num_rows > 0) {
        echo "<p style='color: green;'>✅ عمود access_type موجود</p>";
        
        // عرض تفاصيل العمود
        $column_info = $access_result->fetch_assoc();
        echo "<p><strong>تفاصيل العمود:</strong></p>";
        echo "<ul>";
        echo "<li><strong>النوع:</strong> {$column_info['Type']}</li>";
        echo "<li><strong>القيمة الافتراضية:</strong> {$column_info['Default']}</li>";
        echo "<li><strong>يقبل NULL:</strong> {$column_info['Null']}</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ عمود access_type غير موجود، سيتم إنشاؤه</p>";
        
        // محاولة إضافة العمود
        $add_column = "ALTER TABLE accounts ADD COLUMN access_type ENUM('admin_panel', 'cashier_system') DEFAULT 'cashier_system' AFTER role";
        if ($conn->query($add_column)) {
            echo "<p style='color: green;'>✅ تم إضافة عمود access_type بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إضافة عمود access_type: " . $conn->error . "</p>";
        }
    }
    
    // فحص الجداول الموجودة
    echo "<h3>الجداول الموجودة في قاعدة البيانات:</h3>";
    $show_tables = "SHOW TABLES";
    $tables_result = $conn->query($show_tables);
    if ($tables_result) {
        echo "<ul>";
        while ($table = $tables_result->fetch_array()) {
            echo "<li>{$table[0]}</li>";
        }
        echo "</ul>";
    }
    
    // اختبار prepared statements
    echo "<h3>اختبار Prepared Statements:</h3>";
    $test_query = "SELECT COUNT(*) as count FROM accounts WHERE role = ?";
    $stmt = $conn->prepare($test_query);
    if ($stmt) {
        $test_role = 'admin';
        $stmt->bind_param("s", $test_role);
        if ($stmt->execute()) {
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            echo "<p style='color: green;'>✅ Prepared statements تعمل بشكل صحيح</p>";
            echo "<p>عدد المديرين: {$row['count']}</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في تنفيذ prepared statement: " . $stmt->error . "</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color: red;'>❌ فشل في تحضير prepared statement: " . $conn->error . "</p>";
    }
    
    // اختبار ترميز UTF-8
    echo "<h3>اختبار الترميز:</h3>";
    $charset = $conn->character_set_name();
    echo "<p>ترميز قاعدة البيانات: <strong>$charset</strong></p>";
    if ($charset === 'utf8mb4' || $charset === 'utf8') {
        echo "<p style='color: green;'>✅ الترميز صحيح</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ قد تحتاج لتعديل الترميز</p>";
    }
    
    echo "<hr>";
    echo "<h2>النتيجة:</h2>";
    echo "<p style='color: green; font-weight: bold;'>✅ جميع الاختبارات الأساسية نجحت</p>";
    echo "<p>يمكنك الآن تشغيل ملف التكوين الرئيسي</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='setup_permissions_system.php' style='background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;'>العودة لملف التكوين</a></p>";

?>
