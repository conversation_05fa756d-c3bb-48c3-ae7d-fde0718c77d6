<?php
/**
 * نظام فحص الصلاحيات المخصص لنظام الكاشير
 */

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once 'db_connection.php';
include_once 'encryption_functions.php';
include_once 'permissions_system.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['account_id'])) {
    header('Location: index.php');
    exit();
}

$encrypted_account_id = $_SESSION['account_id'];
$account_id = decrypt($encrypted_account_id, $key);

if (!$account_id) {
    header('Location: index.php');
    exit();
}

// التحقق من حالة الحساب ونوع الوصول
$sql = "SELECT status, role, access_type FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$stmt->bind_result($status, $role, $access_type);
$stmt->fetch();
$stmt->close();

if ($status !== 'active') {
    session_destroy();
    header('Location: index.php?error=account_inactive');
    exit();
}

// إنشاء نظام الصلاحيات
$permissions_system = new PermissionsSystem($conn, $key);

/**
 * فحص صلاحيات نظام الكاشير
 */
function checkCashierPermission($module_name, $permission_name = 'access', $redirect_on_fail = true) {
    global $permissions_system, $access_type;
    
    // التحقق من أن المستخدم يستخدم نظام الكاشير
    if ($access_type !== 'cashier_system') {
        if ($redirect_on_fail) {
            header('Location: stores.php');
            exit();
        }
        return false;
    }
    
    // فحص الصلاحية
    if (!$permissions_system->hasPermission($module_name, $permission_name)) {
        if ($redirect_on_fail) {
            header('HTTP/1.0 403 Forbidden');
            include 'cashier_access_denied.php';
            exit();
        }
        return false;
    }
    
    return true;
}

/**
 * فحص صلاحية إنشاء فاتورة شراء
 */
function canCreatePurchaseInvoice() {
    return checkCashierPermission('cashier_invoices', 'create_purchase', false);
}

/**
 * فحص صلاحية إنشاء فاتورة بيع
 */
function canCreateSaleInvoice() {
    return checkCashierPermission('cashier_invoices', 'create_sale', false);
}

/**
 * فحص صلاحية تقفيل الوردية
 */
function canCloseShift() {
    return checkCashierPermission('cashier_shift_closure', 'close_shift', false);
}

/**
 * فحص صلاحية تعديل البيانات الشخصية
 */
function canEditProfile() {
    return checkCashierPermission('cashier_account', 'edit_profile', false);
}

/**
 * فحص صلاحية التبديل بين الفروع
 */
function canSwitchStore() {
    return checkCashierPermission('cashier_account', 'switch_store', false);
}

/**
 * فحص الوصول للصفحة الرئيسية للكاشير
 */
function canAccessCashierHome() {
    return checkCashierPermission('cashier_home', 'access', false);
}

/**
 * فحص الوصول لإضافة الفواتير
 */
function canAccessInvoices() {
    return checkCashierPermission('cashier_invoices', 'access', false);
}

/**
 * فحص الوصول لتقفيل الوردية
 */
function canAccessShiftClosure() {
    return checkCashierPermission('cashier_shift_closure', 'access', false);
}

/**
 * فحص الوصول لإدارة الحساب
 */
function canAccessAccount() {
    return checkCashierPermission('cashier_account', 'access', false);
}

/**
 * الحصول على قائمة الصلاحيات المتاحة للكاشير
 */
function getCashierPermissions() {
    global $permissions_system;
    
    $permissions = $permissions_system->getUserPermissions();
    $cashier_permissions = [];
    
    // استخراج صلاحيات نظام الكاشير فقط
    $cashier_modules = ['cashier_home', 'cashier_invoices', 'cashier_shift_closure', 'cashier_account'];
    
    foreach ($cashier_modules as $module) {
        if (isset($permissions[$module])) {
            $cashier_permissions[$module] = $permissions[$module];
        }
    }
    
    return $cashier_permissions;
}

/**
 * فحص ما إذا كان المستخدم يمكنه الوصول لنظام الكاشير
 */
function isCashierUser() {
    global $access_type, $role;
    return ($access_type === 'cashier_system' && $role === 'user');
}

/**
 * إعادة توجيه المستخدم للنظام المناسب
 */
function redirectToCorrectSystem() {
    global $access_type;
    
    if ($access_type === 'cashier_system') {
        header('Location: users.php?account_id=' . urlencode($_SESSION['account_id']));
    } else {
        header('Location: stores.php');
    }
    exit();
}

/**
 * عرض رسالة خطأ مخصصة لنظام الكاشير
 */
function showCashierError($message) {
    echo "
    <!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>خطأ في الصلاحية</title>
        <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap' rel='stylesheet'>
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                color: #333;
            }
            .error-container {
                background: white;
                padding: 40px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                text-align: center;
                max-width: 500px;
                width: 90%;
            }
            .error-icon {
                font-size: 64px;
                color: #e74c3c;
                margin-bottom: 20px;
            }
            .error-title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
            .error-message {
                font-size: 16px;
                color: #666;
                line-height: 1.6;
                margin-bottom: 30px;
            }
            .back-button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                transition: transform 0.3s ease;
            }
            .back-button:hover {
                transform: translateY(-2px);
                color: white;
            }
        </style>
    </head>
    <body>
        <div class='error-container'>
            <div class='error-icon'>🚫</div>
            <div class='error-title'>ليس لديك صلاحية</div>
            <div class='error-message'>$message</div>
            <a href='users.php?account_id=" . urlencode($_SESSION['account_id']) . "' class='back-button'>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </body>
    </html>
    ";
}

/**
 * فحص الصلاحيات وعرض رسالة خطأ مخصصة
 */
function requireCashierPermission($module_name, $permission_name = 'access', $custom_message = null) {
    if (!checkCashierPermission($module_name, $permission_name, false)) {
        $message = $custom_message ?: "ليس لديك صلاحية للوصول لهذه الصفحة في نظام الكاشير";
        showCashierError($message);
        exit();
    }
}

?>
