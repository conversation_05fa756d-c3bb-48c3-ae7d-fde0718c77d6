<?php
/**
 * ملف إعداد نظام الصلاحيات
 * يقوم بإنشاء الجداول وإدراج البيانات الأساسية
 */
error_reporting(E_ALL);

// تفعيل عرض الأخطاء على الشاشة
ini_set('display_errors', 1);
include 'db_connection.php';

// التحقق من أن المستخدم مدير

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_permissions'])) {
    try {
        // قراءة وتنفيذ ملف إنشاء الجداول
        $create_tables_sql = file_get_contents('sql/create_permissions_tables.sql');
        if ($create_tables_sql === false) {
            throw new Exception('فشل في قراءة ملف إنشاء الجداول');
        }

        // تقسيم الاستعلامات وتنفيذها
        $queries = explode(';', $create_tables_sql);
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                if (!$conn->query($query)) {
                    throw new Exception('خطأ في تنفيذ الاستعلام: ' . $conn->error);
                }
            }
        }
        $success_messages[] = 'تم إنشاء جداول الصلاحيات بنجاح';

        // قراءة وتنفيذ ملف إدراج البيانات
        $insert_data_sql = file_get_contents('sql/insert_permissions_data.sql');
        if ($insert_data_sql === false) {
            throw new Exception('فشل في قراءة ملف البيانات الأساسية');
        }

        // تقسيم الاستعلامات وتنفيذها
        $queries = explode(';', $insert_data_sql);
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                if (!$conn->query($query)) {
                    // تجاهل أخطاء التكرار
                    if (strpos($conn->error, 'Duplicate entry') === false) {
                        throw new Exception('خطأ في إدراج البيانات: ' . $conn->error);
                    }
                }
            }
        }
        $success_messages[] = 'تم إدراج البيانات الأساسية بنجاح';

        // تحديث جدول accounts لإضافة عمود role_id إذا لم يكن موجوداً
        $check_column = $conn->query("SHOW COLUMNS FROM accounts LIKE 'role_id'");
        if ($check_column->num_rows == 0) {
            $conn->query("ALTER TABLE accounts ADD COLUMN role_id INT NULL AFTER role");
            $success_messages[] = 'تم إضافة عمود role_id لجدول الحسابات';
        }

        // ربط الأدوار الموجودة بالجدول الجديد
        $conn->query("UPDATE accounts a 
                     JOIN roles r ON a.role = r.role_name 
                     SET a.role_id = r.role_id 
                     WHERE a.role_id IS NULL");
        $success_messages[] = 'تم ربط الحسابات الموجودة بالأدوار الجديدة';

    } catch (Exception $e) {
        $error_messages[] = $e->getMessage();
    }
}

// فحص حالة النظام
$tables_exist = true;
$required_tables = ['modules', 'permissions', 'roles', 'role_permissions', 'user_permissions'];
foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows == 0) {
        $tables_exist = false;
        break;
    }
}

// إحصائيات النظام
$stats = [];
if ($tables_exist) {
    $stats['modules'] = $conn->query("SELECT COUNT(*) as count FROM modules")->fetch_assoc()['count'];
    $stats['permissions'] = $conn->query("SELECT COUNT(*) as count FROM permissions")->fetch_assoc()['count'];
    $stats['roles'] = $conn->query("SELECT COUNT(*) as count FROM roles")->fetch_assoc()['count'];
    $stats['role_permissions'] = $conn->query("SELECT COUNT(*) as count FROM role_permissions")->fetch_assoc()['count'];
    $stats['user_permissions'] = $conn->query("SELECT COUNT(*) as count FROM user_permissions")->fetch_assoc()['count'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-success {
            border-right: 4px solid #27ae60;
        }
        
        .status-warning {
            border-right: 4px solid #f39c12;
        }
        
        .status-error {
            border-right: 4px solid #e74c3c;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: linear-gradient(135deg, #3f51b5, #5c6bc0);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .setup-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .setup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        
        .setup-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    
    <div class="container">
        <div class="setup-container">
            <h2><i class="fas fa-cogs"></i> إعداد نظام الصلاحيات</h2>
            
            <?php if (!empty($success_messages)): ?>
                <div class="status-card status-success">
                    <h3><i class="fas fa-check-circle"></i> تم بنجاح</h3>
                    <?php foreach ($success_messages as $message): ?>
                        <p><i class="fas fa-check"></i> <?php echo htmlspecialchars($message); ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_messages)): ?>
                <div class="status-card status-error">
                    <h3><i class="fas fa-exclamation-circle"></i> أخطاء</h3>
                    <?php foreach ($error_messages as $message): ?>
                        <p><i class="fas fa-times"></i> <?php echo htmlspecialchars($message); ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <div class="status-card <?php echo $tables_exist ? 'status-success' : 'status-warning'; ?>">
                <h3>
                    <i class="fas fa-<?php echo $tables_exist ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    حالة النظام
                </h3>
                <p>
                    <?php if ($tables_exist): ?>
                        نظام الصلاحيات مُعد ويعمل بشكل صحيح
                    <?php else: ?>
                        نظام الصلاحيات غير مُعد - يحتاج إلى تهيئة
                    <?php endif; ?>
                </p>
            </div>
            
            <?php if ($tables_exist): ?>
                <div class="status-card">
                    <h3><i class="fas fa-chart-bar"></i> إحصائيات النظام</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['modules']; ?></div>
                            <div>الوحدات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['permissions']; ?></div>
                            <div>الصلاحيات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['roles']; ?></div>
                            <div>الأدوار</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['role_permissions']; ?></div>
                            <div>صلاحيات الأدوار</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['user_permissions']; ?></div>
                            <div>صلاحيات المستخدمين</div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="status-card">
                <h3><i class="fas fa-tools"></i> إجراءات الإعداد</h3>
                <p>
                    <?php if ($tables_exist): ?>
                        يمكنك إعادة تشغيل الإعداد لتحديث البيانات أو إضافة بيانات جديدة
                    <?php else: ?>
                        انقر على الزر أدناه لإنشاء جداول نظام الصلاحيات وإدراج البيانات الأساسية
                    <?php endif; ?>
                </p>
                
                <form method="POST" style="margin-top: 20px;">
                    <button type="submit" name="setup_permissions" class="setup-btn">
                        <i class="fas fa-play"></i>
                        <?php echo $tables_exist ? 'إعادة الإعداد' : 'بدء الإعداد'; ?>
                    </button>
                </form>
            </div>
            
            <?php if ($tables_exist): ?>
                <div class="status-card">
                    <h3><i class="fas fa-external-link-alt"></i> روابط مفيدة</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 15px;">
                        <a href="manage_permissions.php" class="setup-btn" style="text-decoration: none; display: inline-block;">
                            <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                        </a>
                        <a href="accounts.php" class="setup-btn" style="text-decoration: none; display: inline-block; background: linear-gradient(135deg, #3498db, #2980b9);">
                            <i class="fas fa-users"></i> إدارة الحسابات
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</body>
</html>
