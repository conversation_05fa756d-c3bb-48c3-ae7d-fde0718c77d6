<?php
include 'db_connection.php';
include 'encryption_functions.php';
include 'cashier_permissions.php';

// فحص صلاحية الوصول لإدارة الحساب
requireCashierPermission('cashier_account', 'access', 'ليس لديك صلاحية للوصول لصفحة إدارة الحساب');

$key = getenv('ENCRYPTION_KEY');
session_start();

$encrypted_account_id = $_GET['account_id'] ?? null;
$account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;

if (!$account_id) {
    die("Account ID not found. Please log in again.");
}

// Fetch user account details
$query = "SELECT username, password, name, phone, img_path, store_id, theme FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    die("User not found.");
}

$username = htmlspecialchars($user['username']);
$password = $user['password']; // Keep the encrypted password as is
$name = htmlspecialchars($user['name']);
$phone = htmlspecialchars($user['phone']);
$imgPath = $user['img_path'] ?? null;

// Fetch branch names from the stores table
$branches_query = "SELECT name FROM stores";
$branches_result = $conn->query($branches_query);
$branches = [];
if ($branches_result->num_rows > 0) {
    while ($row = $branches_result->fetch_assoc()) {
        $branches[] = $row['name'];
    }
}

// Fetch user's branch
$user_branch_query = "SELECT name FROM stores WHERE store_id = ?";
$user_branch_stmt = $conn->prepare($user_branch_query);
$user_branch_stmt->bind_param("i", $user['store_id']);
$user_branch_stmt->execute();
$user_branch_result = $user_branch_stmt->get_result();
$user_branch = $user_branch_result->fetch_assoc()['name'] ?? null;
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>إعدادات الحساب</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <!-- Remove Bootstrap and Tailwind -->
    <link rel="stylesheet" type="text/css" href="web_css/style_index.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            padding: 20px;
            color: #333;
        }

        body.dark-mode {
            background-color: #1c1e21;
            color: #e4e6eb;
        }

        .form-container {
            max-width: 480px;
            margin: 40px auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            direction: rtl;
        }

        .dark-mode .form-container {
            background-color: #242526;
            color: #e4e6eb;
        }

        .form-label {
            font-weight: 600;
            color: #444;
            margin-bottom: 6px;
            display: inline-block;
        }

        .icon-input {
            position: relative;
            margin-bottom: 20px;
        }

        .icon-input i {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
            color: #007bff;
            font-size: 18px;
            pointer-events: none;
        }

        .icon-input input,
        .icon-input input[type="file"] {
            width: 100%;
            border: none;
            border-bottom: 2px solid #ccc;
            padding: 14px 45px 14px 14px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: #f8f8f8;
            border-radius: 8px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .dark-mode .icon-input input,
        .dark-mode .icon-input select {
            background-color: #333;
            color: #e4e6eb;
            border-bottom: 2px solid #555;
        }

        .icon-input input:focus,
        .icon-input input[type="file"]:focus {
            border-color: #007bff;
            background-color: #fff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .dark-mode .icon-input input:focus,
        .dark-mode .icon-input select:focus {
            border-color: #007bff;
            background-color: #444;
        }

        .icon-input #togglePassword {
            left: 16px;
            right: auto;
            cursor: pointer;
            pointer-events: auto;
        }

        .icon-input i:last-child {
            left: 16px;
            right: auto;
        }

        .icon-input select {
            width: 100%;
            border: none;
            border-bottom: 2px solid #ccc;
            padding: 14px 45px 14px 14px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: #f8f8f8;
            border-radius: 8px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .btn-primary {
            display: block;
            width: 100%;
            padding: 14px;
            border-radius: 10px;
            font-weight: bold;
            background-color: #007bff;
            border: none;
            color: #fff;
            text-align: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .btn-primary {
            background-color: #0056b3;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            transform: scale(1.02);
        }

        .account-profile-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 20px;
            display: block;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .account-default-profile {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: #007bff;
            color: #fff;
            font-size: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .custom-file-input {
            display: none;
        }

        .custom-file-label {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 25px;
            background-color: #007bff;
            color: #fff;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            justify-content: center;
            margin-bottom: 20px;
        }

        .custom-file-label:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        .custom-file-label i {
            font-size: 20px;
        }

        .file-name {
            margin-top: 5px;
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
        }

        .header {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 10px 20px;
            background-color: transparent;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logout-icon {
            color: #ff0000;
            font-size: 24px;
            text-decoration: none;
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .logout-icon:hover {
            color: #ff6666;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <header class="header">
        <a href="logout.php" class="logout-icon" title="تسجيل الخروج">
            <i class="fas fa-sign-out-alt"></i>
        </a>
    </header>
    <div class="form-container">
        <?php if ($imgPath): ?>
            <img src="<?= htmlspecialchars($imgPath) ?>" alt="Profile" class="account-profile-img">
        <?php else: ?>
            <div class="account-default-profile">
                <i class="fas fa-user"></i>
            </div>
        <?php endif; ?>
        <form id="update-account-form" action="update_account.php" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="account_id" value="<?= htmlspecialchars($encrypted_account_id) ?>">
            <label for="username" class="form-label">اسم المستخدم</label>
            <div class="icon-input">
                <i class="fas fa-user"></i>
                <input type="text" class="form-control" id="username" name="username" value="<?= $username ?>" required>
            </div>

            <label for="password" class="form-label">كلمة المرور</label>
            <div class="icon-input">
                <i class="fas fa-lock"></i>
                <input type="password" class="form-control" id="password" name="password" value="" placeholder="أدخل كلمة المرور الجديدة إذا رغبت بتغييرها">
            </div>

            <?php if (canEditProfile()): ?>
            <label for="name" class="form-label">الاسم</label>
            <div class="icon-input">
                <i class="fas fa-id-card"></i>
                <input type="text" class="form-control" id="name" name="name" value="<?= $name ?>" required>
            </div>

            <label for="phone" class="form-label">رقم الهاتف</label>
            <div class="icon-input">
                <i class="fas fa-phone"></i>
                <input type="text" class="form-control" id="phone" name="phone" value="<?= $phone ?>" required>
            </div>
            <?php else: ?>
            <label for="name" class="form-label">الاسم</label>
            <div class="icon-input">
                <i class="fas fa-id-card"></i>
                <input type="text" class="form-control" id="name" name="name" value="<?= $name ?>" readonly style="background-color: #f5f5f5;">
            </div>

            <label for="phone" class="form-label">رقم الهاتف</label>
            <div class="icon-input">
                <i class="fas fa-phone"></i>
                <input type="text" class="form-control" id="phone" name="phone" value="<?= $phone ?>" readonly style="background-color: #f5f5f5;">
            </div>
            <?php endif; ?>

            <label for="branch" class="form-label">الفرع</label>
            <div class="icon-input">
                <i class="fas fa-store"></i>
                <?php if (canSwitchStore()): ?>
                <select class="form-control" id="branch" name="branch" required>
                    <option value="" disabled>اختر الفرع</option>
                    <?php foreach ($branches as $branch): ?>
                        <option value="<?= htmlspecialchars($branch) ?>" <?= $branch === $user_branch ? 'selected' : '' ?>>
                            <?= htmlspecialchars($branch) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <?php else: ?>
                <input type="text" class="form-control" value="<?= htmlspecialchars($user_branch) ?>" readonly style="background-color: #f5f5f5;">
                <input type="hidden" name="branch" value="<?= htmlspecialchars($user_branch) ?>">
                <?php endif; ?>
            </div>

            <label for="theme" class="form-label">اختيار الثيم</label>
            <div class="icon-input">
                <i class="fas fa-palette"></i>
                <select class="form-control" id="theme" name="theme" required>
                    <option value="" disabled>اختر الثيم</option>
                    <option value="Light" <?= $user['theme'] === 'Light' ? 'selected' : '' ?>>فاتح</option>
                    <option value="Dark" <?= $user['theme'] === 'Dark' ? 'selected' : '' ?>>داكن</option>
                </select>
            </div>

            <label for="profile-img" class="custom-file-label">
                <i class="fas fa-upload"></i> تغيير الصورة الشخصية
            </label>
            <input type="file" id="profile-img" name="profile_img" accept="image/*" class="custom-file-input">
            <div id="file-name" class="file-name"></div>
            <button type="submit" class="btn-primary" style="font-family: 'Cairo', sans-serif;">حفظ التغييرات</button>
        </form>
        <!-- Add extra space below the form -->
    </div>
    <div style="margin-bottom: 80px;"></div>
    <?php include 'bottom_nav.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.getElementById('update-account-form').addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(this);

            fetch('update_account.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update localStorage for theme
                    const selectedTheme = document.getElementById('theme').value;
                    localStorage.setItem('dark-mode', selectedTheme === 'Dark');

                    Swal.fire({
                        icon: 'success',
                        title: 'تم التعديل بنجاح',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        location.reload(); // Refresh the page after success
                    });
                } else if (data.message === 'لم يتم تعديل أي بيانات.') {
                    Swal.fire({
                        icon: 'info',
                        title: 'لم يتم تعديل أي بيانات',
                        showConfirmButton: false,
                        timer: 2000
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message,
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء إرسال الطلب.',
                    showConfirmButton: true
                });
            });
        });

        // Display uploaded file name
        const profileImgInput = document.getElementById('profile-img');
        const fileNameDisplay = document.getElementById('file-name');

        profileImgInput.addEventListener('change', function () {
            const file = this.files[0];
            if (file) {
                fileNameDisplay.textContent = file.name;
            } else {
                fileNameDisplay.textContent = '';
            }
        });

        // Apply theme based on localStorage
        const body = document.body;

        if (localStorage.getItem('dark-mode') === 'true') {
            body.classList.add('dark-mode');
        } else {
            body.classList.remove('dark-mode');
        }
    </script>
</body>
</html>
