# نظام الصلاحيات المتقدم - Advanced Permissions System

## نظرة عامة

تم تطوير نظام صلاحيات متقدم ومرن لمشروع الوالد ماركت يوفر تحكماً دقيقاً في صلاحيات المستخدمين والوصول للوظائف المختلفة في النظام.

## المميزات الرئيسية

### 🔐 نظام صلاحيات متعدد المستويات
- **صلاحيات الأدوار**: صلاحيات افتراضية لكل دور
- **صلاحيات المستخدمين الخاصة**: إمكانية تخصيص صلاحيات فردية تتجاوز صلاحيات الدور
- **صلاحيات مؤقتة**: إمكانية تعيين تاريخ انتهاء للصلاحيات الخاصة

### 📊 إدارة شاملة
- واجهة سهلة لإدارة صلاحيات المستخدمين
- تسجيل مفصل لجميع تغييرات الصلاحيات
- إحصائيات وتقارير عن استخدام الصلاحيات

### 🛡️ أمان محسن
- فحص الصلاحيات على مستوى الصفحة والوظيفة
- حماية من الوصول غير المصرح به
- تشفير معرفات المستخدمين والفروع

## هيكل قاعدة البيانات

### الجداول الجديدة

#### 1. `modules` - الوحدات/الصفحات
```sql
- module_id: معرف الوحدة
- module_name: اسم الوحدة (بالإنجليزية)
- module_name_ar: اسم الوحدة (بالعربية)
- file_path: مسار الملف
- icon_class: فئة الأيقونة
- parent_module_id: الوحدة الأب (للوحدات الفرعية)
- sort_order: ترتيب العرض
- is_active: حالة التفعيل
```

#### 2. `permissions` - الصلاحيات
```sql
- permission_id: معرف الصلاحية
- permission_name: اسم الصلاحية (view, create, edit, delete, etc.)
- permission_name_ar: اسم الصلاحية بالعربية
- description: وصف الصلاحية
```

#### 3. `roles` - الأدوار
```sql
- role_id: معرف الدور
- role_name: اسم الدور
- role_name_ar: اسم الدور بالعربية
- description: وصف الدور
- is_system_role: هل هو دور نظام أم مخصص
- is_active: حالة التفعيل
```

#### 4. `role_permissions` - صلاحيات الأدوار
```sql
- role_id: معرف الدور
- module_id: معرف الوحدة
- permission_id: معرف الصلاحية
- granted: هل الصلاحية ممنوحة أم لا
```

#### 5. `user_permissions` - صلاحيات المستخدمين الخاصة
```sql
- account_id: معرف المستخدم
- module_id: معرف الوحدة
- permission_id: معرف الصلاحية
- granted: هل الصلاحية ممنوحة أم لا
- expires_at: تاريخ انتهاء الصلاحية (اختياري)
- notes: ملاحظات
```

#### 6. `permission_logs` - سجل تغييرات الصلاحيات
```sql
- action_type: نوع الإجراء (grant, revoke, modify)
- target_type: نوع الهدف (role, user)
- target_id: معرف الهدف
- old_value: القيمة القديمة
- new_value: القيمة الجديدة
- performed_by: من قام بالتغيير
- reason: سبب التغيير
```

## الملفات الجديدة

### 1. `permissions_system.php`
فئة PHP شاملة لإدارة الصلاحيات تتضمن:
- فحص صلاحيات المستخدمين
- منح وسحب الصلاحيات
- إدارة الكاش للأداء المحسن
- تسجيل التغييرات

### 2. `auth_check.php` (محدث)
ملف فحص الصلاحيات المحسن مع:
- دعم نظام الصلاحيات الجديد
- دوال مساعدة للتحقق من الصلاحيات
- التوافق مع الكود القديم

### 3. `manage_permissions.php`
واجهة إدارة الصلاحيات تتضمن:
- اختيار المستخدم
- عرض الصلاحيات بشكل تفاعلي
- تعديل الصلاحيات في الوقت الفعلي

### 4. `setup_permissions.php`
ملف إعداد النظام لـ:
- إنشاء الجداول المطلوبة
- إدراج البيانات الأساسية
- عرض إحصائيات النظام

### 5. `access_denied.php`
صفحة رفض الوصول المحسنة مع:
- تصميم جذاب ومعلومات مفيدة
- روابط للعودة أو الانتقال للصفحة الرئيسية
- تسجيل محاولات الوصول غير المصرح بها

### 6. `export_accounts.php`
ملف تصدير الحسابات إلى Excel مع:
- تنسيق احترافي
- معلومات شاملة
- تسجيل عمليات التصدير

## طريقة الاستخدام

### 1. الإعداد الأولي
```bash
# زيارة صفحة الإعداد
http://yoursite.com/setup_permissions.php
```

### 2. فحص الصلاحيات في الكود
```php
// فحص صلاحية عامة
if ($permissions_system->hasPermission('accounts', 'view')) {
    // عرض البيانات
}

// فحص صلاحية الصفحة
checkPagePermission('accounts', 'edit');

// فحص صلاحية المدير
requireAdmin();
```

### 3. إدارة الصلاحيات
```php
// منح صلاحية خاصة
$permissions_system->grantUserPermission($user_id, 'accounts', 'delete', '2024-12-31', 'صلاحية مؤقتة');

// سحب صلاحية
$permissions_system->revokeUserPermission($user_id, 'accounts', 'delete', 'انتهاء المهمة');
```

## الأدوار الافتراضية

### 1. مدير النظام (admin)
- صلاحيات كاملة على جميع الوحدات
- إدارة الصلاحيات والمستخدمين
- الوصول لجميع التقارير

### 2. مدير فرع (store_manager)
- إدارة فرع واحد
- صلاحيات كاملة على العمليات التشغيلية
- عرض التقارير الخاصة بالفرع

### 3. مشتري (purchaser)
- إدارة المشتريات والمصاريف
- عرض وتعديل الأصناف
- صلاحيات محدودة

### 4. مستخدم (user)
- عرض الأصناف وإنشاء فواتير البيع
- صلاحيات أساسية للعمليات اليومية

### 5. تاجر (dealer)
- فواتير البيع فقط
- عرض الأصناف المتاحة

## الصلاحيات المتاحة

- **view**: عرض البيانات
- **create**: إنشاء بيانات جديدة
- **edit**: تعديل البيانات
- **delete**: حذف البيانات
- **export**: تصدير البيانات
- **import**: استيراد البيانات
- **approve**: الموافقة على العمليات
- **manage_users**: إدارة المستخدمين
- **manage_permissions**: إدارة الصلاحيات
- **view_reports**: عرض التقارير
- **manage_stores**: إدارة الفروع
- **manage_inventory**: إدارة المخزون

## الأمان والحماية

### 1. تشفير البيانات
- جميع معرفات المستخدمين والفروع مشفرة
- استخدام مفاتيح تشفير آمنة

### 2. تسجيل العمليات
- تسجيل جميع تغييرات الصلاحيات
- تتبع من قام بالتغيير ومتى

### 3. فحص الصلاحيات
- فحص على مستوى الصفحة والوظيفة
- منع الوصول غير المصرح به

### 4. إدارة الجلسات
- فحص صحة الجلسة
- إنهاء الجلسة عند عدم النشاط

## الصيانة والتطوير

### إضافة وحدة جديدة
```sql
INSERT INTO modules (module_name, module_name_ar, file_path, icon_class) 
VALUES ('new_module', 'الوحدة الجديدة', 'new_module.php', 'fas fa-new');
```

### إضافة صلاحية جديدة
```sql
INSERT INTO permissions (permission_name, permission_name_ar, description) 
VALUES ('new_permission', 'صلاحية جديدة', 'وصف الصلاحية');
```

### إضافة دور جديد
```sql
INSERT INTO roles (role_name, role_name_ar, description) 
VALUES ('new_role', 'دور جديد', 'وصف الدور');
```

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع ملف السجلات في `permission_logs`
2. تحقق من إعدادات قاعدة البيانات
3. تأكد من صحة ملفات التشفير

## التحديثات المستقبلية

- إضافة واجهة ويب لإدارة الأدوار
- تقارير مفصلة عن استخدام الصلاحيات
- إشعارات عند تغيير الصلاحيات
- نظام موافقات للصلاحيات الحساسة
