<?php
/**
 * تحديث صلاحيات فواتير الشراء للأدوار المختلفة
 */

include_once 'db_connection.php';
include_once 'auth_check.php';

// التأكد من أن المستخدم مدير
requireAdmin();

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث صلاحيات فواتير الشراء</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .role-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .role-header {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .update-btn {
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .update-btn:hover {
            background: #219a52;
        }
        
        .success-msg {
            background: #d5f4e6;
            color: #27ae60;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-right: 4px solid #27ae60;
        }
        
        .error-msg {
            background: #fdeaea;
            color: #e74c3c;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-right: 4px solid #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-user-cog"></i> تحديث صلاحيات فواتير الشراء</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            try {
                switch ($action) {
                    case 'update_purchaser':
                        // تحديث صلاحيات المشتري
                        $purchaser_permissions = [
                            ['purchases', 'view'],
                            ['purchases', 'search_items'],
                            ['purchase_add', 'create'],
                            ['purchase_edit', 'edit'],
                            ['purchase_images', 'upload_invoice_images'],
                            ['purchase_temp', 'access_temp_data']
                        ];
                        
                        foreach ($purchaser_permissions as $perm) {
                            $module_sql = "SELECT module_id FROM modules WHERE module_name = ?";
                            $perm_sql = "SELECT permission_id FROM permissions WHERE permission_name = ?";
                            
                            $module_stmt = $conn->prepare($module_sql);
                            $module_stmt->bind_param("s", $perm[0]);
                            $module_stmt->execute();
                            $module_result = $module_stmt->get_result();
                            
                            $perm_stmt = $conn->prepare($perm_sql);
                            $perm_stmt->bind_param("s", $perm[1]);
                            $perm_stmt->execute();
                            $perm_result = $perm_stmt->get_result();
                            
                            if ($module_result->num_rows > 0 && $perm_result->num_rows > 0) {
                                $module_id = $module_result->fetch_assoc()['module_id'];
                                $permission_id = $perm_result->fetch_assoc()['permission_id'];
                                
                                $insert_sql = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted) VALUES (3, ?, ?, TRUE)";
                                $insert_stmt = $conn->prepare($insert_sql);
                                $insert_stmt->bind_param("ii", $module_id, $permission_id);
                                $insert_stmt->execute();
                            }
                        }
                        echo "<div class='success-msg'>تم تحديث صلاحيات المشتري بنجاح</div>";
                        break;
                        
                    case 'update_store_manager':
                        // تحديث صلاحيات مدير الفرع
                        $manager_permissions = [
                            ['purchases', 'view'],
                            ['purchases', 'search_items'],
                            ['purchases', 'view_invoice_details'],
                            ['purchase_add', 'create'],
                            ['purchase_edit', 'edit'],
                            ['purchase_delete', 'delete'],
                            ['purchase_export', 'export'],
                            ['purchase_reports', 'generate_reports'],
                            ['purchase_images', 'upload_invoice_images'],
                            ['purchase_status', 'manage_invoice_status'],
                            ['purchase_status', 'confirm_invoice'],
                            ['purchase_temp', 'access_temp_data']
                        ];
                        
                        foreach ($manager_permissions as $perm) {
                            $module_sql = "SELECT module_id FROM modules WHERE module_name = ?";
                            $perm_sql = "SELECT permission_id FROM permissions WHERE permission_name = ?";
                            
                            $module_stmt = $conn->prepare($module_sql);
                            $module_stmt->bind_param("s", $perm[0]);
                            $module_stmt->execute();
                            $module_result = $module_stmt->get_result();
                            
                            $perm_stmt = $conn->prepare($perm_sql);
                            $perm_stmt->bind_param("s", $perm[1]);
                            $perm_stmt->execute();
                            $perm_result = $perm_stmt->get_result();
                            
                            if ($module_result->num_rows > 0 && $perm_result->num_rows > 0) {
                                $module_id = $module_result->fetch_assoc()['module_id'];
                                $permission_id = $perm_result->fetch_assoc()['permission_id'];
                                
                                $insert_sql = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted) VALUES (2, ?, ?, TRUE)";
                                $insert_stmt = $conn->prepare($insert_sql);
                                $insert_stmt->bind_param("ii", $module_id, $permission_id);
                                $insert_stmt->execute();
                            }
                        }
                        echo "<div class='success-msg'>تم تحديث صلاحيات مدير الفرع بنجاح</div>";
                        break;
                        
                    case 'update_user':
                        // تحديث صلاحيات المستخدم العادي (قراءة فقط)
                        $user_permissions = [
                            ['purchases', 'view'],
                            ['purchases', 'search_items'],
                            ['purchases', 'view_invoice_details']
                        ];
                        
                        foreach ($user_permissions as $perm) {
                            $module_sql = "SELECT module_id FROM modules WHERE module_name = ?";
                            $perm_sql = "SELECT permission_id FROM permissions WHERE permission_name = ?";
                            
                            $module_stmt = $conn->prepare($module_sql);
                            $module_stmt->bind_param("s", $perm[0]);
                            $module_stmt->execute();
                            $module_result = $module_stmt->get_result();
                            
                            $perm_stmt = $conn->prepare($perm_sql);
                            $perm_stmt->bind_param("s", $perm[1]);
                            $perm_stmt->execute();
                            $perm_result = $perm_stmt->get_result();
                            
                            if ($module_result->num_rows > 0 && $perm_result->num_rows > 0) {
                                $module_id = $module_result->fetch_assoc()['module_id'];
                                $permission_id = $perm_result->fetch_assoc()['permission_id'];
                                
                                $insert_sql = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted) VALUES (4, ?, ?, TRUE)";
                                $insert_stmt = $conn->prepare($insert_sql);
                                $insert_stmt->bind_param("ii", $module_id, $permission_id);
                                $insert_stmt->execute();
                            }
                        }
                        echo "<div class='success-msg'>تم تحديث صلاحيات المستخدم العادي بنجاح</div>";
                        break;
                        
                    case 'update_accountant':
                        // تحديث صلاحيات المحاسب
                        $accountant_permissions = [
                            ['purchases', 'view'],
                            ['purchases', 'search_items'],
                            ['purchases', 'view_invoice_details'],
                            ['purchase_export', 'export'],
                            ['purchase_reports', 'generate_reports']
                        ];
                        
                        foreach ($accountant_permissions as $perm) {
                            $module_sql = "SELECT module_id FROM modules WHERE module_name = ?";
                            $perm_sql = "SELECT permission_id FROM permissions WHERE permission_name = ?";
                            
                            $module_stmt = $conn->prepare($module_sql);
                            $module_stmt->bind_param("s", $perm[0]);
                            $module_stmt->execute();
                            $module_result = $module_stmt->get_result();
                            
                            $perm_stmt = $conn->prepare($perm_sql);
                            $perm_stmt->bind_param("s", $perm[1]);
                            $perm_stmt->execute();
                            $perm_result = $perm_stmt->get_result();
                            
                            if ($module_result->num_rows > 0 && $perm_result->num_rows > 0) {
                                $module_id = $module_result->fetch_assoc()['module_id'];
                                $permission_id = $perm_result->fetch_assoc()['permission_id'];
                                
                                $insert_sql = "INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted) VALUES (6, ?, ?, TRUE)";
                                $insert_stmt = $conn->prepare($insert_sql);
                                $insert_stmt->bind_param("ii", $module_id, $permission_id);
                                $insert_stmt->execute();
                            }
                        }
                        echo "<div class='success-msg'>تم تحديث صلاحيات المحاسب بنجاح</div>";
                        break;
                        
                    default:
                        echo "<div class='error-msg'>إجراء غير صحيح</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error-msg'>خطأ: " . $e->getMessage() . "</div>";
            }
        }
        ?>
        
        <!-- مدير النظام -->
        <div class="role-section">
            <div class="role-header">
                <i class="fas fa-crown"></i> مدير النظام (Admin)
            </div>
            <p>مدير النظام لديه صلاحيات كاملة على جميع وحدات فواتير الشراء تلقائياً.</p>
            <div style="background: #d5f4e6; padding: 15px; border-radius: 5px; color: #27ae60;">
                <i class="fas fa-check-circle"></i> جميع الصلاحيات مُفعلة
            </div>
        </div>
        
        <!-- مدير الفرع -->
        <div class="role-section">
            <div class="role-header">
                <i class="fas fa-user-tie"></i> مدير الفرع (Store Manager)
            </div>
            <p>صلاحيات شاملة لإدارة فواتير الشراء في الفرع:</p>
            <ul>
                <li>عرض وبحث الفواتير</li>
                <li>إضافة وتعديل وحذف الفواتير</li>
                <li>تصدير التقارير</li>
                <li>إدارة حالة الفواتير وتأكيدها</li>
                <li>رفع الصور والوصول للبيانات المؤقتة</li>
            </ul>
            <form method="POST">
                <input type="hidden" name="action" value="update_store_manager">
                <button type="submit" class="update-btn">
                    <i class="fas fa-sync"></i> تحديث صلاحيات مدير الفرع
                </button>
            </form>
        </div>
        
        <!-- المشتري -->
        <div class="role-section">
            <div class="role-header">
                <i class="fas fa-shopping-bag"></i> المشتري (Purchaser)
            </div>
            <p>صلاحيات أساسية لإدارة عمليات الشراء:</p>
            <ul>
                <li>عرض وبحث الفواتير</li>
                <li>إضافة وتعديل الفواتير</li>
                <li>رفع صور الفواتير</li>
                <li>الوصول للبيانات المؤقتة</li>
            </ul>
            <form method="POST">
                <input type="hidden" name="action" value="update_purchaser">
                <button type="submit" class="update-btn">
                    <i class="fas fa-sync"></i> تحديث صلاحيات المشتري
                </button>
            </form>
        </div>
        
        <!-- المستخدم العادي -->
        <div class="role-section">
            <div class="role-header">
                <i class="fas fa-user"></i> المستخدم العادي (User)
            </div>
            <p>صلاحيات قراءة فقط:</p>
            <ul>
                <li>عرض الفواتير</li>
                <li>البحث في الفواتير</li>
                <li>عرض تفاصيل الفواتير</li>
            </ul>
            <form method="POST">
                <input type="hidden" name="action" value="update_user">
                <button type="submit" class="update-btn">
                    <i class="fas fa-sync"></i> تحديث صلاحيات المستخدم العادي
                </button>
            </form>
        </div>
        
        <!-- المحاسب -->
        <div class="role-section">
            <div class="role-header">
                <i class="fas fa-calculator"></i> المحاسب (Accountant)
            </div>
            <p>صلاحيات محاسبية:</p>
            <ul>
                <li>عرض وبحث الفواتير</li>
                <li>عرض تفاصيل الفواتير</li>
                <li>تصدير البيانات</li>
                <li>إنشاء التقارير</li>
            </ul>
            <form method="POST">
                <input type="hidden" name="action" value="update_accountant">
                <button type="submit" class="update-btn">
                    <i class="fas fa-sync"></i> تحديث صلاحيات المحاسب
                </button>
            </form>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="role-section">
            <h3><i class="fas fa-link"></i> روابط مفيدة</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="test_purchase_permissions.php" style="background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-vial"></i> اختبار الصلاحيات
                </a>
                <a href="manage_permissions.php" style="background: #e74c3c; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                </a>
                <a href="purchase_invoices.php?store_id=MWJhZGJlZWY%3D" style="background: #27ae60; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-shopping-cart"></i> فواتير الشراء
                </a>
            </div>
        </div>
    </div>
</body>
</html>
