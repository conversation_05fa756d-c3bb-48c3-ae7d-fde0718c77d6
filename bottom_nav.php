<?php
// تضمين نظام صلاحيات الكاشير
include_once 'cashier_permissions.php';

// Fetch unread notifications count for the specific user
$unreadQuery = "SELECT COUNT(*) as unread_count
                FROM notifications n
                LEFT JOIN notification_reads nr ON n.id = nr.notification_id AND nr.account_id = ?
                WHERE n.store_id = ? AND nr.id IS NULL AND n.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
$unreadStmt = $conn->prepare($unreadQuery);
$unreadStmt->bind_param("ii", $account_id, $store_id);
$unreadStmt->execute();
$unreadResult = $unreadStmt->get_result();
$unreadCount = $unreadResult->fetch_assoc()['unread_count'];

// Fetch the user's profile image path
$query = "SELECT img_path FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$imgPath = $user['img_path'] ?? null;

// Determine the current page
$current_page = basename($_SERVER['PHP_SELF']);
?>
<div class="bottom-nav">
    <?php if (canAccessCashierHome()): ?>
    <a href="users.php?account_id=<?= urlencode($encrypted_account_id) ?>" class="<?= $current_page === 'users.php' ? 'active' : '' ?>">
        <i class="fas fa-home"></i>
        <span>الرئيسية</span>
    </a>
    <?php endif; ?>

    <?php if (canAccessShiftClosure() && canCloseShift()): ?>
    <a href="shift_closure.php?account_id=<?= urlencode($encrypted_account_id) ?>" class="<?= $current_page === 'shift_closure.php' ? 'active' : '' ?>">
        <i class="fas fa-cash-register"></i>
        <span>تقفيل وردية</span>
    </a>
    <?php endif; ?>

    <?php if (canAccessInvoices() && (canCreatePurchaseInvoice() || canCreateSaleInvoice())): ?>
    <a href="add_invoice.php?account_id=<?= urlencode($encrypted_account_id) ?>" class="<?= $current_page === 'add_invoice.php' ? 'active' : '' ?>">
        <i class="fas fa-file-invoice"></i>
        <span>فاتورة</span>
    </a>
    <?php endif; ?>

    <a href="javascript:void(0);" onclick="showNotifications()" id="notifications-icon" class="<?= $current_page === 'notifications.php' ? 'active' : '' ?>">
        <i class="fas fa-bell"></i>
        <span>الإشعارات</span>
        <span class="notification-count"><?= $unreadCount ?></span>
    </a>

    <?php if (canAccessAccount()): ?>
    <a href="user_account.php?account_id=<?= urlencode($encrypted_account_id) ?>" class="<?= $current_page === 'user_account.php' ? 'active' : '' ?>">
        <?php if ($imgPath): ?>
            <img src="<?= htmlspecialchars($imgPath) ?>" alt="Profile" class="nav-profile-img">
        <?php else: ?>
            <div class="nav-default-profile">
                <i class="fas fa-user"></i>
            </div>
        <?php endif; ?>
        <span>الحساب</span>
    </a>
    <?php endif; ?>
</div>

<style>
/* Bottom Navigation Bar Styles */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 9999;
}

.bottom-nav a {
    text-decoration: none;
    color: #333;
    font-size: 0.9rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 5px;
    position: relative;
}

.bottom-nav a.active {
    color: #007bff;
    font-weight: bold;
}

.bottom-nav a i {
    font-size: 22px;
    margin-bottom: 2px;
}

.notification-count {
    position: absolute;
    top: -5px;
    right: -10px;
    background-color: red;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
}

.nav-profile-img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 2px;
}

.nav-default-profile {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: #333;
    margin-bottom: 2px;
}

/* Dark Mode Styles */
.dark-mode .bottom-nav {
    background-color: #242526;
}

.dark-mode .bottom-nav a {
    color: #e4e6eb;
}

.dark-mode .bottom-nav a.active {
    color: #007bff; /* Bright blue for active icon in dark mode */
    font-weight: bold;
}

.dark-mode .bottom-nav a.active i {
    color: #007bff; /* Bright blue for the icon itself */
}

.dark-mode .nav-default-profile {
    background-color: #444;
    color: #e4e6eb;
}
.notification-container {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
        }

        .notification-item {
            background-color: #f1f1f1;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .notification-message {
            font-size: 16px;
            margin: 0;
        }

        .notification-time {
            font-size: 12px;
            color: #888;
        }
</style>


<audio id="notificationSound" src="/sound.mp3" preload="auto"></audio>

    <!-- Firebase SDK for notifications -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let previousUnreadCount = <?= $unreadCount ?>;
        let audioEnabled = false;
        let messaging = null;
        let fcmToken = null;
        let notificationsModalOpen = false; // متغير لتتبع حالة نافذة الإشعارات

        // Firebase Configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDDKMy2wzA0biwyf86G4t9oHDbKHcjnIDs",
            authDomain: "macm-84114.firebaseapp.com",
            projectId: "macm-84114",
            storageBucket: "macm-84114.firebasestorage.app",
            messagingSenderId: "860043675105",
            appId: "1:860043675105:web:72586005d5bd035ff8bea0"
        };

        const vapidKey = "BMVJO7rt5hONuPb0UzJm2B9T52CuXtcjsWDmHKXf8ass2zyctrBrjXWncazpezhWSdBbcrr8pPcegRixWaTiSBI";

        // Initialize Firebase
        try {
            if (!firebase.apps.length) {
                firebase.initializeApp(firebaseConfig);
            }
            messaging = firebase.messaging();
            console.log('✅ Firebase initialized in bottom_nav.php');
        } catch (error) {
            console.error('❌ Firebase initialization error:', error);
        }

        // Add Firebase notification styles
        const firebaseStyles = `
            <style>
                .firebase-notification {
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    z-index: 999999 !important;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: white !important;
                    border-radius: 12px !important;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
                    min-width: 320px !important;
                    max-width: 400px !important;
                    animation: slideInRight 0.5s ease-out !important;
                    backdrop-filter: blur(10px) !important;
                    border: 1px solid rgba(255, 255, 255, 0.2) !important;
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    pointer-events: auto !important;
                }

                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            </style>
        `;

        // Add styles to head
        if (document.head) {
            document.head.insertAdjacentHTML('beforeend', firebaseStyles);
        } else {
            document.addEventListener('DOMContentLoaded', function() {
                document.head.insertAdjacentHTML('beforeend', firebaseStyles);
            });
        }

        // Enable audio after first user interaction
        function enableAudio() {
            if (!audioEnabled) {
                const notificationSound = document.getElementById('notificationSound');
                if (notificationSound) {
                    // Try to play and immediately pause to enable audio context
                    notificationSound.play().then(() => {
                        notificationSound.pause();
                        notificationSound.currentTime = 0;
                        audioEnabled = true;
                        console.log('Audio enabled after user interaction');
                    }).catch(e => {
                        console.log('Audio enable failed:', e.message);
                    });
                }
            }
        }

        // Add event listeners for user interaction
        document.addEventListener('click', enableAudio, { once: true });
        document.addEventListener('touchstart', enableAudio, { once: true });
        document.addEventListener('keydown', enableAudio, { once: true });

        // Show Firebase notification popup
        function showFirebaseNotification(notification, data) {
            console.log('🔔 Showing Firebase notification in bottom_nav.php:', notification);

            // Remove any existing notifications first
            const existingNotifications = document.querySelectorAll('.firebase-notification');
            existingNotifications.forEach(notif => notif.remove());

            // Create notification element
            const notificationElement = document.createElement('div');
            notificationElement.className = 'firebase-notification';

            notificationElement.innerHTML = `
                <div style="display: flex; align-items: flex-start; padding: 16px; gap: 12px;">
                    <div style="background: rgba(255, 255, 255, 0.2); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                        <i class="fas fa-bell" style="font-size: 18px; color: white;"></i>
                    </div>
                    <div style="flex: 1; min-width: 0;">
                        <div style="font-weight: bold; font-size: 16px; margin-bottom: 4px; line-height: 1.3;">${notification.title || 'إشعار جديد'}</div>
                        <div style="font-size: 14px; opacity: 0.9; line-height: 1.4; word-wrap: break-word;">${notification.body || ''}</div>
                    </div>
                    <div onclick="this.parentElement.parentElement.remove()" style="cursor: pointer; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; border-radius: 50%; background: rgba(255, 255, 255, 0.2); transition: background 0.3s ease; flex-shrink: 0;">
                        <i class="fas fa-times" style="font-size: 12px; color: white;"></i>
                    </div>
                </div>
            `;

            // Add to page
            document.body.appendChild(notificationElement);
            console.log('✅ Notification element added to bottom_nav.php body');

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notificationElement.parentNode) {
                    notificationElement.remove();
                }
            }, 5000);

            return notificationElement;
        }

        // Handle foreground messages
        function handleForegroundMessage(payload) {
            console.log('📨 Handling foreground message in bottom_nav.php:', payload);
            const { notification, data } = payload;

            // Show notification popup
            showFirebaseNotification(notification, data);

            // Play notification sound
            const notificationSound = document.getElementById('notificationSound');
            if (notificationSound && audioEnabled) {
                notificationSound.play().catch(e => {
                    console.log('Sound play failed:', e.message);
                });
            } else if (!audioEnabled) {
                console.log('Audio not enabled yet - waiting for user interaction');
            }

            // Update unread count
            updateUnreadCount();
        }

        window.updateUnreadCount = function() {
            // تخطي التحديث إذا كانت نافذة الإشعارات مفتوحة
            if (notificationsModalOpen) {
                console.log('⏸️ Skipping update - Notifications modal is open');
                return;
            }

            fetch('fetch_account_notifications.php')
                .then(response => response.json())
                .then(data => {
                    const currentUnreadCount = data.unreadCount;
                    document.querySelector('.notification-count').textContent = currentUnreadCount;

                    // Play notification sound if unread count increases
                    if (currentUnreadCount > previousUnreadCount) {
                        const notificationSound = document.getElementById('notificationSound');
                        if (notificationSound && audioEnabled) {
                            notificationSound.play().catch(e => {
                                console.log('Sound play failed:', e.message);
                            });
                        } else if (!audioEnabled) {
                            console.log('Audio not enabled yet - waiting for user interaction');
                        }
                    }

                    previousUnreadCount = currentUnreadCount;
                    console.log('🔄 Unread count updated:', currentUnreadCount);
                });
        }

        window.showNotifications = function() {
            // تعيين حالة النافذة كمفتوحة
            notificationsModalOpen = true;
            console.log('🔔 Notifications modal opened - Auto-refresh paused');

            fetch('fetch_account_notifications.php')
                .then(response => response.json())
                .then(data => {
                    Swal.fire({
                        title: 'الإشعارات',
                        html: `
                            <div id="notifications-container" style="max-height: 400px; overflow-y: auto; text-align: right; direction: rtl;">
                                ${data.notifications}
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <a href="view_all_notifications.php?account_id=<?= urlencode($encrypted_account_id) ?>" class="btn btn-primary" style="background-color: #007bff; color: #fff; text-decoration: none; padding: 10px 20px; border-radius: 5px;">
                                    عرض كل الإشعارات
                                </a>
                            </div>
                        `,
                        width: '600px',
                        padding: '3em',
                        background: '#fff',
                        showCloseButton: true,
                        focusConfirm: false,
                        confirmButtonText: 'إغلاق',
                        confirmButtonColor: '#3B82F6',
                        didOpen: () => {
                            console.log('✅ Notifications modal fully opened');
                        },
                        willClose: () => {
                            // إعادة تعيين حالة النافذة كمغلقة
                            notificationsModalOpen = false;
                            console.log('🔔 Notifications modal closed - Auto-refresh resumed');

                            // تحديث فوري للإشعارات بعد إغلاق النافذة
                            setTimeout(() => {
                                updateUnreadCount();
                            }, 500);
                        }
                    });

                    // Log the notification view in the notification_reads table
                    data.notificationData.forEach(notification => {
                        fetch('log_notification_view.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ notification_id: notification.id, account_id: <?= $account_id ?> })
                        });
                    });
                })
                .catch(error => {
                    console.error('Error fetching notifications:', error);
                    notificationsModalOpen = false; // إعادة تعيين الحالة في حالة الخطأ
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ في تحميل الإشعارات',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                });
        }

        // Initialize Firebase Messaging
        async function initializeFirebaseMessaging() {
            try {
                console.log('🔔 Initializing Firebase Messaging in bottom_nav.php...');

                if (!messaging) {
                    console.error('❌ Firebase messaging not available');
                    return;
                }

                // Request notification permission
                const permission = await Notification.requestPermission();
                console.log('🔐 Permission result:', permission);

                if (permission === 'granted') {
                    console.log('✅ Notification permission granted');

                    // Get FCM token
                    fcmToken = await messaging.getToken({ vapidKey: vapidKey });
                    console.log('🎫 FCM Token:', fcmToken);

                    // Save token to server
                    if (fcmToken) {
                        try {
                            const response = await fetch('save_fcm_token.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    token: fcmToken,
                                    account_id: <?= $account_id ?>,
                                    store_id: <?= $store_id ?>
                                })
                            });
                            const result = await response.json();
                            if (result.success) {
                                console.log('✅ FCM token saved successfully');
                            }
                        } catch (error) {
                            console.error('❌ Error saving FCM token:', error);
                        }
                    }

                    // Listen for foreground messages
                    messaging.onMessage((payload) => {
                        console.log('📨 Message received in foreground (bottom_nav.php):', payload);
                        handleForegroundMessage(payload);
                    });

                    console.log('✅ Firebase Messaging initialization completed in bottom_nav.php');
                }
            } catch (error) {
                console.error('❌ Error initializing Firebase messaging in bottom_nav.php:', error);
            }
        }

        // Register Service Worker
        async function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    let registration;
                    try {
                        registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                        console.log('✅ Service Worker registered at root');
                    } catch (e) {
                        registration = await navigator.serviceWorker.register('/elwaled_market/firebase-messaging-sw.js');
                        console.log('✅ Service Worker registered at elwaled_market');
                    }

                    // Note: useServiceWorker is deprecated in Firebase v9+
                    // The service worker will be used automatically
                    console.log('✅ Service Worker registration completed');

                } catch (error) {
                    console.error('❌ Service Worker registration failed:', error);
                }
            }
        }

        // Test function for debugging
        window.testFirebaseNotificationBottomNav = function() {
            console.log('🧪 Testing Firebase notification in bottom_nav.php...');
            const testNotification = {
                title: 'إشعار تجريبي - Bottom Nav',
                body: 'هذا إشعار تجريبي من Bottom Navigation'
            };
            showFirebaseNotification(testNotification, {});
        };

        // Initialize Firebase when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 DOM loaded in bottom_nav.php, initializing Firebase...');
                registerServiceWorker().then(() => {
                    initializeFirebaseMessaging();
                });
            });
        } else {
            console.log('🚀 DOM already loaded in bottom_nav.php, initializing Firebase...');
            registerServiceWorker().then(() => {
                initializeFirebaseMessaging();
            });
        }

        setInterval(updateUnreadCount, 5000); // Refresh unread count every 5 seconds
    </script>

