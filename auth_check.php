<?php
session_start();
include 'db_connection.php';
include 'encryption_functions.php';

if (!isset($_SESSION['account_id'])) {
    header('Location: index.php');
    exit();
}

if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');
$encrypted_account_id = $_SESSION['account_id'];
$account_id = decrypt($encrypted_account_id, $key);

if (!$account_id) {
    header('Location: index.php');
    exit();
}

// التحقق من حالة الحساب
$sql = "SELECT status, role FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$stmt->bind_result($status, $role);
$stmt->fetch();
$stmt->close();

if ($status !== 'active') {
    session_destroy();
    header('Location: index.php?error=account_inactive');
    exit();
}

// تضمين نظام الصلاحيات بعد إعداد قاعدة البيانات والتشفير
include 'permissions_system.php';

// إنشاء نظام الصلاحيات
$permissions_system = new PermissionsSystem($conn, $key);

/**
 * دالة فحص الصلاحية للصفحة الحالية
 */
function checkPagePermission($module_name, $permission = 'view', $redirect_on_fail = true) {
    global $permissions_system;

    if (!$permissions_system->hasPermission($module_name, $permission)) {
        if ($redirect_on_fail) {
            header('HTTP/1.0 403 Forbidden');
            include 'access_denied.php';
            exit();
        }
        return false;
    }
    return true;
}

/**
 * دالة فحص صلاحية المدير (للتوافق مع الكود القديم)
 */
function requireAdmin() {
    global $permissions_system;

    if (!$permissions_system->isAdmin()) {
        header('Location: index.php?error=admin_required');
        exit();
    }
}

/**
 * دالة فحص صلاحيات متعددة
 */
function hasAnyPermission($module_name, $permissions = ['view']) {
    global $permissions_system;

    foreach ($permissions as $permission) {
        if ($permissions_system->hasPermission($module_name, $permission)) {
            return true;
        }
    }
    return false;
}

/**
 * دالة للحصول على جميع صلاحيات المستخدم الحالي
 */
function getCurrentUserPermissions() {
    global $permissions_system;
    return $permissions_system->getUserPermissions();
}
?>
