<?php
include 'db_connection.php';
include 'auth_check.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';

// فحص صلاحية الوصول لفواتير الشراء
checkPagePermission('purchase_invoices', 'access');
$key = getenv('ENCRYPTION_KEY');
$encrypted_store_id = isset($_GET['store_id']) ? $_GET['store_id'] : null;
$store_id = decrypt($_GET['store_id'], $key);

$sql = "SELECT pi.invoice_id, pi.status, pi.total_amount, DATE_FORMAT(pi.created_at, '%Y-%m-%d %r') AS created_at,
               a.name AS account_name
        FROM purchase_invoices pi
        JOIN accounts a ON pi.account_id = a.account_id
        WHERE pi.store_id = ?
        ORDER BY pi.created_at DESC"; // Order by the most recent invoices
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $store_id);
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();
$store_name = '';
if (isset($encrypted_store_id)) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فواتير الشراء</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
<?php include 'sidebar.php'; ?>
<div class="container">
    <h2>فواتير الشراء</h2>

    <?php if (hasAnyPermission('purchase_invoices', ['create'])): ?>
    <button class="add-btn" onclick="window.location.href='add_purchase_invoice.php?store_id=<?php echo urlencode(encrypt($store_id, $key)); ?>&account_id=<?php echo urlencode($_SESSION['account_id']); ?>'">إضافة فاتورة جديدة</button>
    <?php endif; ?>

    <?php if (hasAnyPermission('purchase_invoices', ['export'])): ?>
    <button class="add-btn" onclick="window.location.href='export_purchase_invoices.php?store_id=<?php echo urlencode($encrypted_store_id); ?>'">تصدير إلى Excel</button>
    <?php endif; ?>

    <?php if (hasAnyPermission('purchase_invoices', ['reports'])): ?>
    <button class="add-btn" onclick="showReport()">تقرير</button>
    <?php endif; ?>

    <?php if (hasAnyPermission('purchase_invoices', ['view'])): ?>
    <input type="text" id="item-search-input" class="search-bar" placeholder="ابحث عن صنف...">
    <?php endif; ?>

    <div class="table-responsive">
        <table>
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>الشخص</th>
                    <th>الحالة</th>
                    <th>إجمالي السعر</th>
                    <th>المدفوع</th>
                    <th>الباقي</th>
                    <th>توقيت الفاتورة</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody id="invoice-table-body">
                <?php

                if ($result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $encrypted_invoice_id = encrypt($row['invoice_id'], $key);
                        $encrypted_store_id = encrypt($store_id, $key);
                        $status = strtolower(trim($row['status'])); // Normalize value
                        $status_class = $status === 'confirmed' ? 'confirmed' : 'pending';
                        $status_text = $status === 'confirmed' ? 'مؤكد' : 'معلق';
                        // فحص صلاحية تغيير حالة الفاتورة
                        $can_change_status = hasAnyPermission('purchase_invoices', ['approve']);
                        $status_onclick = ($status === 'pending' && $can_change_status) ? "onclick='changeInvoiceStatus(\"{$encrypted_invoice_id}\")'" : '';

                        // فحص صلاحية عرض تفاصيل الفاتورة
                        $can_view_details = hasAnyPermission('purchase_invoices', ['view']);
                        $invoice_click = $can_view_details ? "onclick='showInvoiceItems(\"{$encrypted_invoice_id}\")'" : '';

                        echo "<tr data-invoice-id='{$row['invoice_id']}'>
                                <td><span class='invoice-number' {$invoice_click}>{$row['invoice_id']}</span></td>
                                <td>{$row['account_name']}</td>
                                <td><span class='status-frame {$status_class}' {$status_onclick}>{$status_text}</span></td>
                                <td>{$row['total_amount']}</td>
                                <td>{$row['total_amount']}</td>
                                <td>0.00</td>
                                <td>{$row['created_at']}</td>
                                <td>";

                        // زر التعديل
                        if (hasAnyPermission('purchase_invoices', ['edit'])) {
                            echo "<button class='action-btn' onclick='window.location.href=\"edit_purchase_invoice.php?store_id={$encrypted_store_id}&invoice_id={$encrypted_invoice_id}\"'>
                                    <i class='fas fa-edit'></i>
                                  </button>";
                        }

                        // زر الحذف
                        if (hasAnyPermission('purchase_invoices', ['delete'])) {
                            echo "<button class='action-btn' onclick='deleteInvoice(\"{$encrypted_invoice_id}\")'>
                                    <i class='fas fa-trash-alt'></i>
                                  </button>";
                        }

                        echo "</td></tr>";

                        // Fetch and display items for this invoice
                        $stmt_items = $conn->prepare("SELECT i.name, p.quantity, i.cost FROM purchases p JOIN items i ON p.item_id = i.item_id WHERE p.invoice_id = ?");
                        $stmt_items->bind_param("i", $row['invoice_id']);
                        $stmt_items->execute();
                        $items_result = $stmt_items->get_result();
                        $stmt_items->close();

                        if ($items_result->num_rows > 0) {
                            echo "<tr class='invoice-items' data-invoice-id='{$row['invoice_id']}' style='display: none;'>
                                    <td colspan='8'>
                                        <table class='table'>
                                            <thead>
                                                <tr>
                                                    <th>اسم الصنف</th>
                                                    <th>الكمية</th>
                                                    <th>السعر</th>
                                                    <th>إجمالي السعر</th> <!-- New column -->
                                                </tr>
                                            </thead>
                                            <tbody>";
                            while ($item = $items_result->fetch_assoc()) {
                                $total_price = $item['quantity'] * $item['cost']; // Calculate total price
                                echo "<tr>
                                        <td>{$item['name']}</td>
                                        <td>{$item['quantity']}</td>
                                        <td>{$item['cost']}</td>
                                        <td>{$total_price}</td> <!-- Display total price -->
                                      </tr>";
                            }
                            echo "      </tbody>
                                        </table>
                                    </td>
                                  </tr>";
                        }
                    }
                } else {
                    echo "<tr><td colspan='8'>لا توجد فواتير حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>
</div>

<div id="invoiceItemsModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>تفاصيل الفاتورة</h2>
        <table>
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                </tr>
            </thead>
            <tbody id="invoiceItemsTableBody">
                <!-- Invoice items will be populated here -->
            </tbody>
        </table>
    </div>
</div>

<div id="reportModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeReportModal()">&times;</span>
        <h2>تقرير إجمالي الفواتير</h2>
        <div id="reportContent">
            <!-- Report content will be populated here -->
        </div>
    </div>
</div>

<script>
    function showInvoiceItems(encryptedInvoiceId) {
        const formData = new FormData();
        formData.append('encrypted_invoice_id', encryptedInvoiceId);

        fetch('fetch_invoice_items.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tableBody = document.getElementById('invoiceItemsTableBody');
                tableBody.innerHTML = '';
                data.items.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${item.price}</td>
                    `;
                    tableBody.appendChild(row);
                });

                // Display invoice images
                const modalContent = document.querySelector('#invoiceItemsModal .modal-content');
                const oldImagesContainer = modalContent.querySelector('.invoice-images');
                if (oldImagesContainer) {
                    oldImagesContainer.remove();
                }
                const imagesContainer = document.createElement('div');
                imagesContainer.classList.add('invoice-images');
                imagesContainer.style.marginTop = '20px';
                imagesContainer.innerHTML = '<h3>صور الفاتورة:</h3>';
                data.images.forEach(imagePath => {
                    const img = document.createElement('img');
                    img.src = imagePath;
                    img.style.width = '100px';
                    img.style.marginBottom = '10px';
                    img.style.cursor = 'pointer';
                    img.onclick = () => openImageModal(imagePath);
                    imagesContainer.appendChild(img);
                });
                modalContent.appendChild(imagesContainer);

                document.getElementById('invoiceItemsModal').classList.add("active"); // Use "active" class for showing
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في جلب تفاصيل الفاتورة',
                    text: 'حدث خطأ أثناء جلب تفاصيل الفاتورة. حاول مرة أخرى.'
                });
            }
        })
        .catch(error => {
            console.error("Error fetching invoice items:", error);
        });
    }

    function openImageModal(imagePath) {
        Swal.fire({
            imageUrl: imagePath,
            imageAlt: 'صورة الفاتورة',
            showCloseButton: true,
            showConfirmButton: false,
            width: '80%',
        });
    }

    document.querySelectorAll('.close').forEach(span => {
        span.onclick = function() {
            // Identify the parent modal
            const modal = span.closest('.modal');
            if (!modal) return;

            if (modal.id === 'reportModal') {
                // For reportModal: hide using style.display
                modal.style.display = 'none';
            } else {
                // For other modals: remove the "active" class
                modal.classList.remove('active');
            }
        };
    });

    // Unified handler for closing modals when clicking outside
    window.addEventListener('click', event => {
        if (event.target.classList.contains('modal')) {
            if (event.target.id === 'reportModal') {
                event.target.style.display = 'none'; // Hide reportModal
            } else {
                event.target.classList.remove('active'); // Hide other modals with "active" class
            }
        }
    });

    function changeInvoiceStatus(encryptedInvoiceId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "سيتم تغيير حالة الفاتورة إلى مؤكد!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، تأكيد',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('encrypted_invoice_id', encryptedInvoiceId);

                fetch('change_invoice_status.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم تغيير حالة الفاتورة بنجاح',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            location.reload(); // Refresh the page after success
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: data.message || 'حدث خطأ أثناء تغيير حالة الفاتورة.'
                        });
                    }
                })
                .catch(error => {
                    console.error("Error changing invoice status:", error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تغيير حالة الفاتورة.'
                    });
                });
            }
        });
    }

    function deleteInvoice(encryptedInvoiceId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('encrypted_invoice_id', encryptedInvoiceId);

                fetch('delete_invoice.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم حذف الفاتورة بنجاح',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            location.reload(); // Refresh the page after the success message
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ',
                            text: 'حدث خطأ أثناء حذف الفاتورة.'
                        });
                    }
                })
                .catch(error => {
                    console.error("Error deleting invoice:", error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء حذف الفاتورة.'
                    });
                });
            }
        });
    }

    function showPopupMessage(message, type, duration = 3000) {
        Swal.fire({
            icon: type,
            title: message,
            showConfirmButton: false,
            timer: duration
        });
    }

    <?php if (isset($_SESSION['message'])): ?>
        showPopupMessage('<?php echo $_SESSION['message']; ?>', '<?php echo $_SESSION['message_type']; ?>');
        <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
    <?php endif; ?>

    document.getElementById('item-search-input').addEventListener('input', function () {
        const query = this.value.toLowerCase();
        const rows = document.querySelectorAll('#invoice-table-body tr[data-invoice-id]');
        rows.forEach(row => {
            const invoiceId = row.getAttribute('data-invoice-id');
            const itemRows = document.querySelectorAll(`.invoice-items[data-invoice-id="${invoiceId}"] tbody tr`);
            let matchFound = false;

            itemRows.forEach(itemRow => {
                const itemName = itemRow.cells[0].textContent.toLowerCase();
                if (itemName.includes(query)) {
                    matchFound = true;
                    itemRow.style.display = '';
                } else {
                    itemRow.style.display = 'none';
                }
            });

            if (query === '') {
                // Reset all rows if the search bar is cleared
                itemRows.forEach(itemRow => itemRow.style.display = '');
                row.style.display = '';
                const invoiceItemsRow = document.querySelector(`.invoice-items[data-invoice-id="${invoiceId}"]`);
                if (invoiceItemsRow) {
                    invoiceItemsRow.style.display = 'none';
                }
            } else {
                row.style.display = matchFound ? '' : 'none';
                const invoiceItemsRow = document.querySelector(`.invoice-items[data-invoice-id="${invoiceId}"]`);
                if (invoiceItemsRow) {
                    invoiceItemsRow.style.display = matchFound ? '' : 'none';
                }
            }
        });
    });

    function showReport() {
        fetch('get_invoice_report.php?store_id=<?php echo urlencode($encrypted_store_id); ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const reportContent = document.getElementById('reportContent');
                    reportContent.innerHTML = `
                        <p><strong>إجمالي عدد الفواتير:</strong> ${data.total_invoices}</p>
                        <p><strong>إجمالي الفواتير:</strong> ${data.total_price} جنيه</p>
                        <p><strong>الفواتير المؤكدة:</strong> ${data.confirmed_count} فاتورة - إجمالي: ${data.confirmed_total} جنيه</p>
                        <p><strong>الفواتير المعلقة:</strong> ${data.pending_count} فاتورة - إجمالي: ${data.pending_total} جنيه</p>
                    `;
                    document.getElementById('reportModal').style.display = 'block';
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء جلب التقرير.'
                    });
                }
            })
            .catch(error => {
                console.error("Error fetching report:", error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء جلب التقرير.'
                });
            });
    }

    function closeReportModal() {
        document.getElementById('reportModal').style.display = 'none';
    }

    window.onclick = function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    }


</script>
<?php include 'notifications.php'; ?>
</body>
</html>

<?php
$conn->close();
?>
