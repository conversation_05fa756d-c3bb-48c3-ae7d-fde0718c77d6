-- إدراج البيانات الأساسية لنظام الصلاحيات

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (permission_name, permission_name_ar, description) VALUES
('view', 'عرض', 'صلاحية عرض البيانات'),
('create', 'إنشاء', 'صلاحية إنشاء بيانات جديدة'),
('edit', 'تعديل', 'صلاحية تعديل البيانات الموجودة'),
('delete', 'حذف', 'صلاحية حذف البيانات'),
('export', 'تصدير', 'صلاحية تصدير البيانات'),
('import', 'استيراد', 'صلاحية استيراد البيانات'),
('approve', 'موافقة', 'صلاحية الموافقة على العمليات'),
('manage_users', 'إدارة المستخدمين', 'صلاحية إدارة حسابات المستخدمين'),
('manage_permissions', 'إدارة الصلاحيات', 'صلاحية إدارة صلاحيات النظام'),
('view_reports', 'عرض التقارير', 'صلاحية عرض التقارير'),
('manage_stores', 'إدارة الفروع', 'صلاحية إدارة الفروع'),
('manage_inventory', 'إدارة المخزون', 'صلاحية إدارة المخزون والجرد');

-- إدراج الوحدات الأساسية
INSERT INTO modules (module_name, module_name_ar, description, file_path, icon_class, sort_order) VALUES
('dashboard', 'لوحة التحكم', 'الصفحة الرئيسية للنظام', 'stores.php', 'fas fa-home', 1),
('stores', 'إدارة الفروع', 'إدارة فروع المتجر', 'stores.php', 'fas fa-store', 2),
('categories', 'إدارة التصنيفات', 'إدارة تصنيفات المنتجات', 'categories.php', 'fas fa-tags', 3),
('items', 'إدارة الأصناف', 'إدارة أصناف المنتجات', 'items.php', 'fas fa-box', 4),
('purchases', 'فواتير الشراء', 'إدارة فواتير الشراء', 'purchase_invoices.php', 'fas fa-shopping-cart', 5),
('sales', 'فواتير البيع', 'إدارة فواتير البيع', 'wholesale_invoices.php', 'fas fa-receipt', 6),
('expenses', 'المصاريف', 'إدارة مصاريف الفرع', 'expenses.php', 'fas fa-money-bill-wave', 7),
('transfers', 'تحويلات الرصيد', 'إدارة تحويلات الرصيد', 'balance_transfers.php', 'fas fa-exchange-alt', 8),
('inventory', 'الجرد', 'إدارة عمليات الجرد', 'inventory.php', 'fas fa-warehouse', 9),
('shift_closures', 'تقفيل الورديات', 'إدارة تقفيل الورديات', 'shift_closures.php', 'fas fa-clock', 10),
('accounts', 'إدارة الحسابات', 'إدارة حسابات المستخدمين', 'accounts.php', 'fas fa-users', 11),
('reports', 'التقارير', 'عرض التقارير المختلفة', 'generate_report.php', 'fas fa-chart-bar', 12),
('notifications', 'الإشعارات', 'إدارة الإشعارات', 'notifications.php', 'fas fa-bell', 13),
('settings', 'الإعدادات', 'إعدادات النظام', 'profile.php', 'fas fa-cog', 14);

-- إدراج الأدوار الأساسية
INSERT INTO roles (role_name, role_name_ar, description, is_system_role) VALUES
('admin', 'مدير النظام', 'مدير النظام له صلاحيات كاملة', TRUE),
('store_manager', 'مدير فرع', 'مدير فرع له صلاحيات إدارة فرع واحد', TRUE),
('purchaser', 'مشتري', 'مسؤول عن عمليات الشراء', TRUE),
('user', 'مستخدم', 'مستخدم عادي بصلاحيات محدودة', TRUE),
('dealer', 'تاجر', 'تاجر له صلاحيات البيع فقط', TRUE),
('accountant', 'محاسب', 'مسؤول عن العمليات المحاسبية', FALSE),
('inventory_manager', 'مدير مخزون', 'مسؤول عن إدارة المخزون والجرد', FALSE);

-- ربط الوحدات بالصلاحيات
INSERT INTO module_permissions (module_id, permission_id, is_required) 
SELECT m.module_id, p.permission_id, 
       CASE 
           WHEN p.permission_name = 'view' THEN TRUE 
           ELSE FALSE 
       END as is_required
FROM modules m 
CROSS JOIN permissions p 
WHERE p.permission_name IN ('view', 'create', 'edit', 'delete', 'export');

-- إعطاء صلاحيات كاملة للمدير
INSERT INTO role_permissions (role_id, module_id, permission_id, granted, created_by)
SELECT r.role_id, m.module_id, p.permission_id, TRUE, 1
FROM roles r
CROSS JOIN modules m
CROSS JOIN permissions p
WHERE r.role_name = 'admin';

-- إعطاء صلاحيات محددة لمدير الفرع
INSERT INTO role_permissions (role_id, module_id, permission_id, granted, created_by)
SELECT r.role_id, m.module_id, p.permission_id, TRUE, 1
FROM roles r
CROSS JOIN modules m
CROSS JOIN permissions p
WHERE r.role_name = 'store_manager'
AND m.module_name IN ('categories', 'items', 'purchases', 'sales', 'expenses', 'transfers', 'inventory', 'shift_closures', 'reports')
AND p.permission_name IN ('view', 'create', 'edit', 'delete', 'export');

-- إعطاء صلاحيات للمشتري
INSERT INTO role_permissions (role_id, module_id, permission_id, granted, created_by)
SELECT r.role_id, m.module_id, p.permission_id, TRUE, 1
FROM roles r
CROSS JOIN modules m
CROSS JOIN permissions p
WHERE r.role_name = 'purchaser'
AND m.module_name IN ('items', 'purchases', 'expenses')
AND p.permission_name IN ('view', 'create', 'edit');

-- إعطاء صلاحيات للمستخدم العادي
INSERT INTO role_permissions (role_id, module_id, permission_id, granted, created_by)
SELECT r.role_id, m.module_id, p.permission_id, TRUE, 1
FROM roles r
CROSS JOIN modules m
CROSS JOIN permissions p
WHERE r.role_name = 'user'
AND m.module_name IN ('items', 'sales', 'inventory')
AND p.permission_name IN ('view', 'create');

-- إعطاء صلاحيات للتاجر
INSERT INTO role_permissions (role_id, module_id, permission_id, granted, created_by)
SELECT r.role_id, m.module_id, p.permission_id, TRUE, 1
FROM roles r
CROSS JOIN modules m
CROSS JOIN permissions p
WHERE r.role_name = 'dealer'
AND m.module_name IN ('items', 'sales')
AND p.permission_name IN ('view', 'create');
