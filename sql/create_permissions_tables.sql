-- إن<PERSON>ا<PERSON> جداول نظام الصلاحيات

-- جدول الوحدات/الصفحات
CREATE TABLE IF NOT EXISTS modules (
    module_id INT AUTO_INCREMENT PRIMARY KEY,
    module_name VARCHAR(100) NOT NULL UNIQUE,
    module_name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    file_path VARCHAR(255),
    icon_class VARCHAR(50),
    parent_module_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_module_id) REFERENCES modules(module_id) ON DELETE SET NULL
);

-- جدول الصلاحيات/الأذونات
CREATE TABLE IF NOT EXISTS permissions (
    permission_id INT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    permission_name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط الوحدات بالصلاحيات
CREATE TABLE IF NOT EXISTS module_permissions (
    module_permission_id INT AUTO_INCREMENT PRIMARY KEY,
    module_id INT NOT NULL,
    permission_id INT NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    UNIQUE KEY unique_module_permission (module_id, permission_id)
);

-- جدول الأدوار المحسن
CREATE TABLE IF NOT EXISTS roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_name_ar VARCHAR(50) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول صلاحيات الأدوار
CREATE TABLE IF NOT EXISTS role_permissions (
    role_permission_id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    module_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES accounts(account_id) ON DELETE SET NULL,
    UNIQUE KEY unique_role_module_permission (role_id, module_id, permission_id)
);

-- جدول صلاحيات المستخدمين الخاصة (تجاوز صلاحيات الدور)
CREATE TABLE IF NOT EXISTS user_permissions (
    user_permission_id INT AUTO_INCREMENT PRIMARY KEY,
    account_id INT NOT NULL,
    module_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    notes TEXT,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES accounts(account_id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_module_permission (account_id, module_id, permission_id)
);

-- جدول سجل تغييرات الصلاحيات
CREATE TABLE IF NOT EXISTS permission_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    action_type ENUM('grant', 'revoke', 'modify') NOT NULL,
    target_type ENUM('role', 'user') NOT NULL,
    target_id INT NOT NULL,
    module_id INT,
    permission_id INT,
    old_value BOOLEAN,
    new_value BOOLEAN,
    performed_by INT,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE SET NULL,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE SET NULL,
    FOREIGN KEY (performed_by) REFERENCES accounts(account_id) ON DELETE SET NULL
);
