<?php
/**
 * ملف تكوين نظام الصلاحيات
 * يقوم بإعداد وتكوين نظام الصلاحيات الكامل للمشروع
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين الملفات المطلوبة
include_once 'db_connection.php';
include_once 'encryption_functions.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// التحقق من صلاحية المدير
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('يجب أن تكون مدير للوصول لهذه الصفحة');
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'check_database':
                $result = checkAndCreateTables($conn);
                echo json_encode(['success' => true, 'step' => 1, 'message' => 'تم فحص وإنشاء الجداول بنجاح', 'details' => $result]);
                break;

            case 'create_modules':
                $result = createModules($conn);
                echo json_encode(['success' => true, 'step' => 2, 'message' => 'تم إنشاء الوحدات بنجاح', 'details' => $result]);
                break;

            case 'create_permissions':
                $result = createPermissions($conn);
                echo json_encode(['success' => true, 'step' => 3, 'message' => 'تم إنشاء الصلاحيات بنجاح', 'details' => $result]);
                break;

            case 'setup_role_permissions':
                $result = setupRolePermissions($conn);
                echo json_encode(['success' => true, 'step' => 4, 'message' => 'تم تكوين صلاحيات الأدوار بنجاح', 'details' => $result]);
                break;

            case 'setup_admin_permissions':
                $result = setupAdminPermissions($conn);
                echo json_encode(['success' => true, 'step' => 5, 'message' => 'تم إنشاء صلاحيات المدير بنجاح', 'details' => $result]);
                break;

            case 'setup_access_types':
                $result = setupAccessTypes($conn);
                echo json_encode(['success' => true, 'step' => 6, 'message' => 'تم تكوين نوع الوصول بنجاح', 'details' => $result]);
                break;

            case 'test_system':
                $result = testSystem($conn, $key);
                echo json_encode(['success' => true, 'step' => 7, 'message' => 'تم اختبار النظام بنجاح', 'details' => $result]);
                break;

            case 'reset_system':
                $result = resetSystem($conn);
                echo json_encode(['success' => true, 'message' => 'تم إعادة تعيين النظام بنجاح', 'details' => $result]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'إجراء غير معروف']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

/**
 * دوال التكوين
 */

function checkAndCreateTables($conn) {
    $results = [];

    try {
        // 1. فحص وتعديل جدول الحسابات
        $results[] = "🔍 فحص جدول الحسابات...";

        // فحص وجود عمود access_type
        $check_column = "SHOW COLUMNS FROM accounts LIKE 'access_type'";
        $result = $conn->query($check_column);

        if ($result->num_rows == 0) {
            $add_column = "ALTER TABLE accounts ADD COLUMN access_type ENUM('admin_panel', 'cashier_system') DEFAULT 'cashier_system' AFTER role";
            $conn->query($add_column);
            $results[] = "✅ تم إضافة عمود access_type لجدول الحسابات (افتراضي: نظام الكاشير)";
        } else {
            $results[] = "✅ عمود access_type موجود في جدول الحسابات";
        }

        // تحديث الحسابات الموجودة لتكون افتراضياً نظام الكاشير (عدا المديرين)
        $update_existing = "UPDATE accounts SET access_type = 'cashier_system' WHERE role != 'admin' AND (access_type IS NULL OR access_type = '')";
        $conn->query($update_existing);
        $results[] = "✅ تم تحديث الحسابات الموجودة لنظام الكاشير (عدا المديرين)";

        // تحديث المديرين ليكونوا في النظام الإداري
        $update_admins = "UPDATE accounts SET access_type = 'admin_panel' WHERE role = 'admin'";
        $conn->query($update_admins);
        $results[] = "✅ تم تحديث المديرين للنظام الإداري";

        // 2. إنشاء جدول الأدوار إذا لم يكن موجوداً
        $create_roles = "CREATE TABLE IF NOT EXISTS roles (
            role_id INT AUTO_INCREMENT PRIMARY KEY,
            role_name VARCHAR(50) NOT NULL UNIQUE,
            role_name_ar VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_roles)) {
            $results[] = "✅ جدول الأدوار جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول الأدوار: " . $conn->error;
        }

        // 3. إنشاء جدول الوحدات
        $create_modules = "CREATE TABLE IF NOT EXISTS modules (
            module_id INT AUTO_INCREMENT PRIMARY KEY,
            module_name VARCHAR(100) NOT NULL UNIQUE,
            module_name_ar VARCHAR(150) NOT NULL,
            description TEXT,
            file_path VARCHAR(255),
            icon_class VARCHAR(100),
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_modules)) {
            $results[] = "✅ جدول الوحدات جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول الوحدات: " . $conn->error;
        }

        // 4. إنشاء جدول الصلاحيات
        $create_permissions = "CREATE TABLE IF NOT EXISTS permissions (
            permission_id INT AUTO_INCREMENT PRIMARY KEY,
            permission_name VARCHAR(100) NOT NULL UNIQUE,
            permission_name_ar VARCHAR(150) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_permissions)) {
            $results[] = "✅ جدول الصلاحيات جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول الصلاحيات: " . $conn->error;
        }

        // 5. إنشاء جدول صلاحيات الأدوار
        $create_role_permissions = "CREATE TABLE IF NOT EXISTS role_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_id INT NOT NULL,
            module_id INT NOT NULL,
            permission_id INT NOT NULL,
            granted BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_role_module_permission (role_id, module_id, permission_id),
            FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_role_permissions)) {
            $results[] = "✅ جدول صلاحيات الأدوار جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول صلاحيات الأدوار: " . $conn->error;
        }

        // 6. إنشاء جدول صلاحيات المستخدمين
        $create_user_permissions = "CREATE TABLE IF NOT EXISTS user_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_id INT NOT NULL,
            module_id INT NOT NULL,
            permission_id INT NOT NULL,
            granted BOOLEAN DEFAULT FALSE,
            created_by INT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_module_permission (account_id, module_id, permission_id),
            FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES accounts(account_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_user_permissions)) {
            $results[] = "✅ جدول صلاحيات المستخدمين جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول صلاحيات المستخدمين: " . $conn->error;
        }

        // 7. إنشاء جدول فروع المستخدمين
        $create_user_stores = "CREATE TABLE IF NOT EXISTS user_stores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            store_id INT NOT NULL,
            granted BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_store (user_id, store_id),
            FOREIGN KEY (user_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
            FOREIGN KEY (store_id) REFERENCES stores(store_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_user_stores)) {
            $results[] = "✅ جدول فروع المستخدمين جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول فروع المستخدمين: " . $conn->error;
        }

        // 8. إنشاء جدول سجل الصلاحيات
        $create_permission_logs = "CREATE TABLE IF NOT EXISTS permission_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            action_type ENUM('grant', 'revoke', 'modify') NOT NULL,
            target_type ENUM('user', 'role') NOT NULL,
            target_id INT NOT NULL,
            module_id INT,
            permission_id INT,
            old_value BOOLEAN,
            new_value BOOLEAN,
            performed_by INT,
            reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (module_id) REFERENCES modules(module_id) ON DELETE SET NULL,
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE SET NULL,
            FOREIGN KEY (performed_by) REFERENCES accounts(account_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_permission_logs)) {
            $results[] = "✅ جدول سجل الصلاحيات جاهز";
        } else {
            $results[] = "❌ خطأ في إنشاء جدول سجل الصلاحيات: " . $conn->error;
        }

        // 9. إدراج الأدوار الأساسية إذا لم تكن موجودة
        $roles_data = [
            [1, 'admin', 'مدير النظام', 'مدير النظام الرئيسي مع جميع الصلاحيات'],
            [2, 'store_manager', 'مدير الفرع', 'مدير فرع مع صلاحيات إدارة الفرع'],
            [3, 'purchaser', 'المشتري', 'مسؤول عن عمليات الشراء'],
            [4, 'user', 'مستخدم عادي', 'مستخدم عادي مع صلاحيات محدودة'],
            [5, 'cashier', 'كاشير', 'كاشير مع صلاحيات نظام الكاشير'],
            [6, 'accountant', 'محاسب', 'محاسب مع صلاحيات المحاسبة']
        ];

        foreach ($roles_data as $role) {
            $check_role = "SELECT role_id FROM roles WHERE role_id = {$role[0]}";
            $role_result = $conn->query($check_role);

            if ($role_result->num_rows == 0) {
                $insert_role = "INSERT INTO roles (role_id, role_name, role_name_ar, description) VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_role);
                $stmt->bind_param("isss", $role[0], $role[1], $role[2], $role[3]);
                $stmt->execute();
                $stmt->close();
                $results[] = "✅ تم إضافة دور: {$role[2]}";
            }
        }

        $results[] = "🎉 تم إنشاء وفحص جميع الجداول بنجاح";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في إنشاء الجداول: " . $e->getMessage();
    }

    return implode('<br>', $results);
}

function createModules($conn) {
    $results = [];

    // حذف الوحدات الموجودة
    $conn->query("DELETE FROM modules");
    $results[] = "🗑️ تم حذف الوحدات الموجودة";

    // إنشاء الوحدات الجديدة
    $modules = [
        // وحدات النظام الإداري
        ['dashboard', 'لوحة التحكم', 'الصفحة الرئيسية ولوحة التحكم', 'stores.php', 'fas fa-home', 1],
        ['store_management', 'إدارة الفرع', 'إدارة وتكوين الفرع', 'store_shortcuts.php', 'fas fa-cogs', 2],
        ['reports', 'التقارير', 'تقارير الأصناف والمبيعات', 'item_reports.php', 'fas fa-chart-line', 3],
        ['categories', 'التصنيفات', 'إدارة تصنيفات المنتجات', 'categories.php', 'fas fa-th-list', 4],
        ['items', 'الأصناف', 'إدارة أصناف المنتجات', 'item_store.php', 'fas fa-boxes', 5],
        ['purchase_invoices', 'فواتير الشراء', 'إدارة فواتير الشراء', 'purchase_invoices.php', 'fas fa-file-invoice-dollar', 6],
        ['wholesale_invoices', 'فواتير البيع بالجملة', 'إدارة فواتير البيع بالجملة', 'wholesale_invoices.php', 'fas fa-shopping-cart', 7],
        ['inventory', 'الجرد', 'إدارة الجرد والمخزون', 'inventory.php', 'fas fa-warehouse', 8],
        ['accounts', 'الحسابات', 'إدارة حسابات المستخدمين', 'accounts.php', 'fas fa-user-cog', 9],
        ['transfer_items', 'نقل الأصناف', 'نقل الأصناف بين الفروع', 'transfer_items.php', 'fas fa-exchange-alt', 10],
        ['expired_items', 'صلاحيات الأصناف', 'إدارة صلاحيات الأصناف', 'expired_items.php', 'fas fa-hourglass-end', 11],
        ['expenses', 'المصاريف', 'إدارة مصاريف الفرع', 'expenses.php', 'fas fa-money-bill-wave', 12],
        ['shift_closures', 'تقفيل الورديات', 'إدارة تقفيل الورديات', 'shift_closures.php', 'fas fa-cash-register', 13],
        ['balance_transfers', 'تحويلات الرصيد', 'إدارة تحويلات الرصيد', 'balance_transfers.php', 'fas fa-wallet', 14],
        ['notifications', 'الإشعارات', 'إدارة الإشعارات', 'notifications_page.php', 'fas fa-bell', 15],
        ['send_notifications', 'إرسال الإشعارات', 'إرسال إشعارات للمستخدمين', 'send_notifications.php', 'fas fa-paper-plane', 16],

        // وحدات نظام الكاشير
        ['cashier_home', 'الرئيسية', 'الصفحة الرئيسية لنظام الكاشير', 'users.php', 'fas fa-home', 17],
        ['cashier_invoices', 'إضافة الفواتير', 'إضافة فواتير الشراء والبيع', 'add_invoice.php', 'fas fa-file-invoice', 18],
        ['cashier_shift_closure', 'تقفيل الوردية', 'تقفيل وردية الكاشير', 'shift_closure.php', 'fas fa-cash-register', 19],
        ['cashier_account', 'إدارة الحساب', 'إدارة بيانات الحساب الشخصي', 'user_account.php', 'fas fa-user-cog', 20]
    ];

    $stmt = $conn->prepare("INSERT INTO modules (module_name, module_name_ar, description, file_path, icon_class, sort_order) VALUES (?, ?, ?, ?, ?, ?)");

    foreach ($modules as $module) {
        $stmt->bind_param("sssssi", $module[0], $module[1], $module[2], $module[3], $module[4], $module[5]);
        $stmt->execute();
        $results[] = "➕ تم إنشاء وحدة: {$module[1]}";
    }

    $stmt->close();
    $results[] = "✅ تم إنشاء " . count($modules) . " وحدة بنجاح";

    return implode('<br>', $results);
}

function createPermissions($conn) {
    $results = [];

    // حذف الصلاحيات الموجودة
    $conn->query("DELETE FROM permissions");
    $results[] = "🗑️ تم حذف الصلاحيات الموجودة";

    // إنشاء الصلاحيات الجديدة
    $permissions = [
        ['access', 'الوصول', 'صلاحية الوصول للصفحة أو الوحدة'],
        ['view', 'عرض', 'صلاحية عرض البيانات'],
        ['create', 'إنشاء', 'صلاحية إنشاء عناصر جديدة'],
        ['edit', 'تعديل', 'صلاحية تعديل العناصر الموجودة'],
        ['delete', 'حذف', 'صلاحية حذف العناصر'],
        ['export', 'تصدير', 'صلاحية تصدير البيانات'],
        ['import', 'استيراد', 'صلاحية استيراد البيانات'],
        ['manage', 'إدارة', 'صلاحية إدارة شاملة للوحدة'],
        ['approve', 'موافقة', 'صلاحية الموافقة على العمليات'],
        ['reports', 'التقارير', 'صلاحية عرض وإنشاء التقارير'],

        // صلاحيات خاصة بنظام الكاشير
        ['create_purchase', 'إنشاء فاتورة شراء', 'صلاحية إنشاء فواتير الشراء في نظام الكاشير'],
        ['create_sale', 'إنشاء فاتورة بيع', 'صلاحية إنشاء فواتير البيع بالجملة في نظام الكاشير'],
        ['close_shift', 'تقفيل الوردية', 'صلاحية تقفيل الوردية في نظام الكاشير'],
        ['edit_profile', 'تعديل البيانات الشخصية', 'صلاحية تعديل البيانات الشخصية'],
        ['switch_store', 'التبديل بين الفروع', 'صلاحية التبديل بين الفروع المختلفة']
    ];

    $stmt = $conn->prepare("INSERT INTO permissions (permission_name, permission_name_ar, description) VALUES (?, ?, ?)");

    foreach ($permissions as $permission) {
        $stmt->bind_param("sss", $permission[0], $permission[1], $permission[2]);
        $stmt->execute();
        $results[] = "➕ تم إنشاء صلاحية: {$permission[1]}";
    }

    $stmt->close();
    $results[] = "✅ تم إنشاء " . count($permissions) . " صلاحية بنجاح";

    return implode('<br>', $results);
}

function setupRolePermissions($conn) {
    $results = [];

    // حذف صلاحيات الأدوار الموجودة
    $conn->query("DELETE FROM role_permissions");
    $results[] = "🗑️ تم حذف صلاحيات الأدوار الموجودة";

    // إضافة صلاحيات المدير (جميع الصلاحيات على جميع الوحدات)
    $admin_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                    SELECT 1, m.module_id, p.permission_id, TRUE
                    FROM modules m
                    CROSS JOIN permissions p";
    $conn->query($admin_query);
    $results[] = "✅ تم إضافة جميع الصلاحيات للمدير";

    // إضافة صلاحيات مدير الفرع
    $store_manager_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                           SELECT 2, m.module_id, p.permission_id, TRUE
                           FROM modules m
                           CROSS JOIN permissions p
                           WHERE m.module_name IN ('dashboard', 'store_management', 'reports', 'categories', 'items', 'purchase_invoices', 'wholesale_invoices', 'inventory', 'transfer_items', 'expired_items', 'expenses', 'shift_closures', 'balance_transfers')
                           AND p.permission_name IN ('access', 'view', 'create', 'edit', 'delete', 'export', 'manage', 'approve', 'reports')";
    $conn->query($store_manager_query);
    $results[] = "✅ تم إضافة صلاحيات مدير الفرع";

    // إضافة صلاحيات المشتري
    $purchaser_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                       SELECT 3, m.module_id, p.permission_id, TRUE
                       FROM modules m
                       CROSS JOIN permissions p
                       WHERE m.module_name IN ('dashboard', 'items', 'purchase_invoices', 'reports')
                       AND p.permission_name IN ('access', 'view', 'create', 'edit', 'export', 'reports')";
    $conn->query($purchaser_query);
    $results[] = "✅ تم إضافة صلاحيات المشتري";

    // إضافة صلاحيات المستخدم العادي (للنظام الإداري)
    $user_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                  SELECT 4, m.module_id, p.permission_id, TRUE
                  FROM modules m
                  CROSS JOIN permissions p
                  WHERE m.module_name IN ('dashboard', 'items', 'purchase_invoices', 'wholesale_invoices', 'reports')
                  AND p.permission_name IN ('access', 'view', 'reports')";
    $conn->query($user_query);
    $results[] = "✅ تم إضافة صلاحيات المستخدم العادي للنظام الإداري";

    // إضافة صلاحيات نظام الكاشير للمستخدمين العاديين

    // صلاحيات الصفحة الرئيسية للكاشير
    $cashier_home_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                          SELECT 4, m.module_id, p.permission_id, TRUE
                          FROM modules m
                          CROSS JOIN permissions p
                          WHERE m.module_name = 'cashier_home'
                          AND p.permission_name IN ('access', 'view')";
    $conn->query($cashier_home_query);
    $results[] = "✅ تم إضافة صلاحيات الصفحة الرئيسية للكاشير";

    // صلاحيات إضافة الفواتير (شراء وبيع)
    $cashier_invoices_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                              SELECT 4, m.module_id, p.permission_id, TRUE
                              FROM modules m
                              CROSS JOIN permissions p
                              WHERE m.module_name = 'cashier_invoices'
                              AND p.permission_name IN ('access', 'view', 'create_purchase', 'create_sale')";
    $conn->query($cashier_invoices_query);
    $results[] = "✅ تم إضافة صلاحيات إضافة الفواتير (شراء وبيع)";

    // صلاحيات تقفيل الوردية (بدون أي صلاحيات إضافية)
    $cashier_shift_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                           SELECT 4, m.module_id, p.permission_id, TRUE
                           FROM modules m
                           CROSS JOIN permissions p
                           WHERE m.module_name = 'cashier_shift_closure'
                           AND p.permission_name IN ('access', 'view', 'close_shift')";
    $conn->query($cashier_shift_query);
    $results[] = "✅ تم إضافة صلاحيات تقفيل الوردية";

    // صلاحيات إدارة الحساب (تعديل البيانات والتبديل بين الفروع)
    $cashier_account_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                             SELECT 4, m.module_id, p.permission_id, TRUE
                             FROM modules m
                             CROSS JOIN permissions p
                             WHERE m.module_name = 'cashier_account'
                             AND p.permission_name IN ('access', 'view', 'edit_profile', 'switch_store')";
    $conn->query($cashier_account_query);
    $results[] = "✅ تم إضافة صلاحيات إدارة الحساب (تعديل البيانات والتبديل بين الفروع)";

    // إضافة صلاحيات المحاسب
    $accountant_query = "INSERT INTO role_permissions (role_id, module_id, permission_id, granted)
                        SELECT 6, m.module_id, p.permission_id, TRUE
                        FROM modules m
                        CROSS JOIN permissions p
                        WHERE m.module_name IN ('dashboard', 'purchase_invoices', 'wholesale_invoices', 'expenses', 'reports')
                        AND p.permission_name IN ('access', 'view', 'export', 'reports')";
    $conn->query($accountant_query);
    $results[] = "✅ تم إضافة صلاحيات المحاسب";

    return implode('<br>', $results);
}

function setupAdminPermissions($conn) {
    $results = [];

    // التأكد من وجود صلاحيات المدير
    $check_query = "SELECT COUNT(*) as count FROM role_permissions rp
                   JOIN roles r ON rp.role_id = r.role_id
                   WHERE r.role_name = 'admin' AND rp.granted = TRUE";
    $result = $conn->query($check_query);
    $count = $result->fetch_assoc()['count'];

    $results[] = "📊 عدد صلاحيات المدير: $count";

    if ($count > 0) {
        $results[] = "✅ صلاحيات المدير موجودة ومكتملة";
    } else {
        $results[] = "❌ لم يتم العثور على صلاحيات المدير";
    }

    return implode('<br>', $results);
}

function setupAccessTypes($conn) {
    $results = [];

    try {
        // التأكد من وجود عمود access_type
        $check_column = "SHOW COLUMNS FROM accounts LIKE 'access_type'";
        $result = $conn->query($check_column);

        if ($result->num_rows == 0) {
            // إضافة العمود إذا لم يكن موجوداً (افتراضي: نظام الكاشير)
            $add_column = "ALTER TABLE accounts ADD COLUMN access_type ENUM('admin_panel', 'cashier_system') DEFAULT 'cashier_system' AFTER role";
            $conn->query($add_column);
            $results[] = "✅ تم إضافة عمود نوع الوصول (افتراضي: نظام الكاشير)";
        } else {
            $results[] = "✅ عمود نوع الوصول موجود مسبقاً";
        }

        // تحديث جميع المستخدمين غير المديرين ليكون وصولهم لنظام الكاشير
        $update_non_admins = "UPDATE accounts SET access_type = 'cashier_system' WHERE role != 'admin'";
        $conn->query($update_non_admins);
        $results[] = "✅ تم تحديث نوع الوصول لجميع المستخدمين غير المديرين إلى نظام الكاشير";

        // تحديث المديرين للنظام الإداري
        $update_admins = "UPDATE accounts SET access_type = 'admin_panel' WHERE role = 'admin'";
        $conn->query($update_admins);
        $results[] = "✅ تم تحديث المديرين للنظام الإداري";

        // فحص عدد المستخدمين المحدثين
        $count_query = "SELECT COUNT(*) as count FROM accounts WHERE role = 'user' AND access_type = 'cashier_system'";
        $count_result = $conn->query($count_query);
        $count = $count_result->fetch_assoc()['count'];
        $results[] = "📊 عدد المستخدمين في نظام الكاشير: $count";

        // فحص عدد المستخدمين في النظام الإداري
        $admin_count_query = "SELECT COUNT(*) as count FROM accounts WHERE access_type = 'admin_panel'";
        $admin_count_result = $conn->query($admin_count_query);
        $admin_count = $admin_count_result->fetch_assoc()['count'];
        $results[] = "📊 عدد المستخدمين في النظام الإداري: $admin_count";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في تكوين نوع الوصول: " . $e->getMessage();
    }

    return implode('<br>', $results);
}

function testSystem($conn, $key) {
    $results = [];

    try {
        // تضمين نظام الصلاحيات
        include_once 'permissions_system.php';
        $permissions_system = new PermissionsSystem($conn, $key);
        $results[] = "✅ تم إنشاء نظام الصلاحيات بنجاح";

        // اختبار فحص المدير
        $is_admin = $permissions_system->isAdmin();
        $results[] = $is_admin ? "✅ فحص المدير يعمل بشكل صحيح" : "❌ فحص المدير لا يعمل";

        // اختبار صلاحيات محددة
        $test_permissions = [
            ['accounts', 'manage', 'إدارة الحسابات'],
            ['purchase_invoices', 'access', 'الوصول لفواتير الشراء'],
            ['purchase_invoices', 'create', 'إنشاء فواتير الشراء']
        ];

        foreach ($test_permissions as $test) {
            $has_permission = $permissions_system->hasPermission($test[0], $test[1]);
            $status = $has_permission ? "✅" : "❌";
            $results[] = "$status {$test[2]}: " . ($has_permission ? 'مسموح' : 'غير مسموح');
        }

        // اختبار دوال المساعدة
        include_once 'auth_check.php';
        $has_any = hasAnyPermission('accounts', ['manage']);
        $results[] = $has_any ? "✅ دالة hasAnyPermission تعمل بشكل صحيح" : "❌ دالة hasAnyPermission لا تعمل";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في الاختبار: " . $e->getMessage();
    }

    return implode('<br>', $results);
}

function resetSystem($conn) {
    $results = [];

    try {
        // حذف جميع البيانات
        $conn->query("DELETE FROM permission_logs");
        $results[] = "🗑️ تم حذف سجل الصلاحيات";

        $conn->query("DELETE FROM user_permissions");
        $results[] = "🗑️ تم حذف صلاحيات المستخدمين";

        $conn->query("DELETE FROM user_stores");
        $results[] = "🗑️ تم حذف فروع المستخدمين";

        $conn->query("DELETE FROM role_permissions");
        $results[] = "🗑️ تم حذف صلاحيات الأدوار";

        $conn->query("DELETE FROM modules");
        $results[] = "🗑️ تم حذف الوحدات";

        $conn->query("DELETE FROM permissions");
        $results[] = "🗑️ تم حذف الصلاحيات";

        $results[] = "✅ تم إعادة تعيين النظام بالكامل";

    } catch (Exception $e) {
        $results[] = "❌ خطأ في إعادة التعيين: " . $e->getMessage();
    }

    return implode('<br>', $results);
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تكوين نظام الصلاحيات</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .setup-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }

        .setup-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .step-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .step-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .step-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .step-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .setup-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin: 10px 5px;
        }

        .setup-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .setup-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }

        th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: bold;
        }

        .status-icon {
            font-size: 18px;
            margin-left: 10px;
        }

        .links-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }

        .nav-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
            <i class="fas fa-cogs"></i> تكوين نظام الصلاحيات
        </h1>

        <!-- شريط التقدم -->
        <div class="setup-section">
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar" style="width: 0%;"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="progressText">جاري التحضير...</span>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="setup-section">
            <div class="section-header">
                <i class="fas fa-info-circle"></i> معلومات نظام الصلاحيات
            </div>

            <div class="info-box">
                <h4>🎯 الهدف من النظام:</h4>
                <ul style="margin-right: 20px; line-height: 1.8;">
                    <li><strong>تحكم دقيق:</strong> إدارة صلاحيات المستخدمين على مستوى الوحدات والعمليات</li>
                    <li><strong>أمان محسن:</strong> حماية شاملة لجميع صفحات ووظائف النظام</li>
                    <li><strong>مرونة في الإدارة:</strong> تخصيص صلاحيات مختلفة لكل دور ومستخدم</li>
                    <li><strong>إدارة الفروع:</strong> تحديد الفروع المسموحة لكل مستخدم</li>
                    <li><strong>تسجيل العمليات:</strong> تتبع جميع تغييرات الصلاحيات</li>
                </ul>

                <h4>🏗️ مكونات النظام:</h4>
                <ul style="margin-right: 20px; line-height: 1.8;">
                    <li><strong>الوحدات (Modules):</strong> تمثل صفحات وأقسام النظام</li>
                    <li><strong>الصلاحيات (Permissions):</strong> العمليات المختلفة (عرض، إنشاء، تعديل، حذف، إلخ)</li>
                    <li><strong>الأدوار (Roles):</strong> مجموعات صلاحيات محددة مسبقاً</li>
                    <li><strong>صلاحيات المستخدمين:</strong> صلاحيات خاصة تتجاوز صلاحيات الدور</li>
                    <li><strong>صلاحيات الفروع:</strong> تحديد الفروع المسموحة لكل مستخدم</li>
                    <li><strong>نوع الوصول:</strong> تحديد النظام (إداري/كاشير) لكل مستخدم</li>
                </ul>

                <h4>🗄️ الجداول التي سيتم إنشاؤها:</h4>
                <ul style="margin-right: 20px; line-height: 1.8;">
                    <li><strong>roles:</strong> جدول الأدوار والمناصب</li>
                    <li><strong>modules:</strong> جدول وحدات النظام</li>
                    <li><strong>permissions:</strong> جدول الصلاحيات المختلفة</li>
                    <li><strong>role_permissions:</strong> ربط الأدوار بالصلاحيات</li>
                    <li><strong>user_permissions:</strong> صلاحيات خاصة للمستخدمين</li>
                    <li><strong>user_stores:</strong> ربط المستخدمين بالفروع</li>
                    <li><strong>permission_logs:</strong> سجل تغييرات الصلاحيات</li>
                    <li><strong>accounts (تحديث):</strong> إضافة عمود نوع الوصول</li>
                </ul>
            </div>
        </div>

        <!-- خطوات التكوين -->
        <div class="setup-section">
            <div class="section-header">
                <i class="fas fa-list-ol"></i> خطوات التكوين
            </div>

            <div id="setupSteps">
                <div class="step-result" id="step1">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 1: فحص وإنشاء قاعدة البيانات والجداول المطلوبة</span>
                </div>

                <div class="step-result" id="step2">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 2: إنشاء الوحدات الأساسية</span>
                </div>

                <div class="step-result" id="step3">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 3: إنشاء الصلاحيات الأساسية</span>
                </div>

                <div class="step-result" id="step4">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 4: تكوين صلاحيات الأدوار</span>
                </div>

                <div class="step-result" id="step5">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 5: إنشاء صلاحيات المدير</span>
                </div>

                <div class="step-result" id="step6">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 6: تكوين نوع الوصول للمستخدمين</span>
                </div>

                <div class="step-result" id="step7">
                    <i class="fas fa-hourglass-half status-icon"></i>
                    <span>الخطوة 7: اختبار النظام</span>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="setup-button" onclick="startSetup()">
                    <i class="fas fa-play"></i> بدء التكوين
                </button>
                <button class="setup-button" onclick="resetSystem()" style="background: #e74c3c;">
                    <i class="fas fa-redo"></i> إعادة تعيين النظام
                </button>
            </div>
        </div>

        <!-- نتائج التكوين -->
        <div class="setup-section" id="resultsSection" style="display: none;">
            <div class="section-header">
                <i class="fas fa-chart-bar"></i> نتائج التكوين
            </div>
            <div id="setupResults"></div>
        </div>

        <!-- روابط التنقل -->
        <div class="setup-section">
            <div class="section-header">
                <i class="fas fa-link"></i> روابط النظام
            </div>
            <div class="links-section">
                <a href="manage_permissions.php" class="nav-link">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                </a>
                <a href="stores.php" class="nav-link">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-users"></i> إدارة الحسابات
                </a>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 7;

        function updateProgress(step, status, message) {
            const stepElement = document.getElementById(`step${step}`);
            const icon = stepElement.querySelector('.status-icon');
            const text = stepElement.querySelector('span');

            stepElement.className = `step-result step-${status}`;

            if (status === 'success') {
                icon.className = 'fas fa-check-circle status-icon';
                icon.style.color = '#155724';
            } else if (status === 'error') {
                icon.className = 'fas fa-times-circle status-icon';
                icon.style.color = '#721c24';
            } else if (status === 'warning') {
                icon.className = 'fas fa-exclamation-triangle status-icon';
                icon.style.color = '#856404';
            }

            if (message) {
                text.innerHTML = message;
            }

            // تحديث شريط التقدم
            const progress = (step / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressText').textContent = `الخطوة ${step} من ${totalSteps}`;
        }

        async function startSetup() {
            document.querySelector('button[onclick="startSetup()"]').disabled = true;
            document.getElementById('resultsSection').style.display = 'block';

            try {
                // الخطوة 1: فحص وإنشاء قاعدة البيانات
                updateProgress(1, 'warning', 'الخطوة 1: جاري فحص وإنشاء الجداول...');
                await executeStep('check_database');

                // الخطوة 2: إنشاء الوحدات
                updateProgress(2, 'warning', 'الخطوة 2: جاري إنشاء الوحدات...');
                await executeStep('create_modules');

                // الخطوة 3: إنشاء الصلاحيات
                updateProgress(3, 'warning', 'الخطوة 3: جاري إنشاء الصلاحيات...');
                await executeStep('create_permissions');

                // الخطوة 4: تكوين صلاحيات الأدوار
                updateProgress(4, 'warning', 'الخطوة 4: جاري تكوين صلاحيات الأدوار...');
                await executeStep('setup_role_permissions');

                // الخطوة 5: إنشاء صلاحيات المدير
                updateProgress(5, 'warning', 'الخطوة 5: جاري إنشاء صلاحيات المدير...');
                await executeStep('setup_admin_permissions');

                // الخطوة 6: تكوين نوع الوصول
                updateProgress(6, 'warning', 'الخطوة 6: جاري تكوين نوع الوصول...');
                await executeStep('setup_access_types');

                // الخطوة 7: اختبار النظام
                updateProgress(7, 'warning', 'الخطوة 7: جاري اختبار النظام...');
                await executeStep('test_system');

                document.getElementById('progressText').textContent = 'تم الانتهاء من التكوين بنجاح!';

            } catch (error) {
                console.error('خطأ في التكوين:', error);
                alert('حدث خطأ أثناء التكوين: ' + error.message);
            }

            document.querySelector('button[onclick="startSetup()"]').disabled = false;
        }

        async function executeStep(action) {
            const response = await fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${action}`
            });

            const result = await response.json();

            if (result.success) {
                updateProgress(result.step, 'success', result.message);

                if (result.details) {
                    const resultsDiv = document.getElementById('setupResults');
                    resultsDiv.innerHTML += `<div class="step-success">${result.details}</div>`;
                }
            } else {
                updateProgress(result.step, 'error', result.message);
                throw new Error(result.message);
            }

            // انتظار قصير لإظهار التقدم
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        async function resetSystem() {
            if (!confirm('هل أنت متأكد من إعادة تعيين نظام الصلاحيات؟ سيتم حذف جميع الصلاحيات الحالية.')) {
                return;
            }

            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=reset_system'
                });

                const result = await response.json();

                if (result.success) {
                    alert('تم إعادة تعيين النظام بنجاح');
                    location.reload();
                } else {
                    alert('فشل في إعادة تعيين النظام: ' + result.message);
                }
            } catch (error) {
                alert('حدث خطأ: ' + error.message);
            }
        }
    </script>
</body>
</html>
