<?php
/**
 * اختبار حفظ الصلاحيات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>اختبار حفظ الصلاحيات</h1>";

try {
    // تضمين الملفات المطلوبة
    include_once 'db_connection.php';
    include_once 'encryption_functions.php';
    
    // تحميل متغيرات البيئة
    if (file_exists(__DIR__ . '/.env')) {
        $dotenv = parse_ini_file(__DIR__ . '/.env');
        foreach ($dotenv as $key => $value) {
            putenv("$key=$value");
        }
    }
    
    $key = getenv('ENCRYPTION_KEY');
    
    // محاكاة تسجيل دخول مدير
    if (!isset($_SESSION['account_id'])) {
        $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
        $admin_result = $conn->query($admin_query);
        
        if ($admin_result && $admin_result->num_rows > 0) {
            $admin = $admin_result->fetch_assoc();
            $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
            $_SESSION['username'] = 'admin_test';
            $_SESSION['role'] = 'admin';
            echo "<p style='color: green;'>✅ تم تسجيل دخول المدير بنجاح</p>";
        }
    }
    
    // تضمين نظام الصلاحيات
    include_once 'permissions_system.php';
    
    // إنشاء نظام الصلاحيات
    $permissions_system = new PermissionsSystem($conn, $key);
    echo "<p style='color: green;'>✅ تم إنشاء نظام الصلاحيات بنجاح</p>";
    
    // البحث عن مستخدم للاختبار
    $user_query = "SELECT account_id, username FROM accounts WHERE role != 'admin' AND status = 'active' LIMIT 1";
    $user_result = $conn->query($user_query);
    
    if ($user_result && $user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        $test_user_id = $user['account_id'];
        $test_username = $user['username'];
        
        echo "<h2>اختبار مع المستخدم: $test_username (ID: $test_user_id)</h2>";
        
        // اختبار منح صلاحية
        echo "<h3>1. اختبار منح صلاحية</h3>";
        try {
            $result = $permissions_system->grantUserPermission($test_user_id, 'purchase_invoices', 'create');
            echo "<p style='color: green;'>✅ تم منح صلاحية إنشاء فواتير الشراء بنجاح</p>";
            
            // فحص الصلاحية في قاعدة البيانات
            $check_query = "SELECT granted FROM user_permissions up 
                           JOIN modules m ON up.module_id = m.module_id 
                           JOIN permissions p ON up.permission_id = p.permission_id 
                           WHERE up.account_id = ? AND m.module_name = 'purchase_invoices' AND p.permission_name = 'create'";
            $stmt = $conn->prepare($check_query);
            $stmt->bind_param("i", $test_user_id);
            $stmt->execute();
            $check_result = $stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $row = $check_result->fetch_assoc();
                $granted = $row['granted'];
                echo "<p style='color: " . ($granted ? 'green' : 'red') . ";'>";
                echo ($granted ? '✅' : '❌') . " الصلاحية في قاعدة البيانات: " . ($granted ? 'مفعلة' : 'معطلة');
                echo "</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ لم يتم العثور على السجل في قاعدة البيانات</p>";
            }
            $stmt->close();
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في منح الصلاحية: " . $e->getMessage() . "</p>";
        }
        
        // اختبار سحب صلاحية
        echo "<h3>2. اختبار سحب صلاحية</h3>";
        try {
            $result = $permissions_system->revokeUserPermission($test_user_id, 'purchase_invoices', 'create');
            echo "<p style='color: green;'>✅ تم سحب صلاحية إنشاء فواتير الشراء بنجاح</p>";
            
            // فحص الصلاحية في قاعدة البيانات
            $check_query = "SELECT granted FROM user_permissions up 
                           JOIN modules m ON up.module_id = m.module_id 
                           JOIN permissions p ON up.permission_id = p.permission_id 
                           WHERE up.account_id = ? AND m.module_name = 'purchase_invoices' AND p.permission_name = 'create'";
            $stmt = $conn->prepare($check_query);
            $stmt->bind_param("i", $test_user_id);
            $stmt->execute();
            $check_result = $stmt->get_result();
            
            if ($check_result->num_rows > 0) {
                $row = $check_result->fetch_assoc();
                $granted = $row['granted'];
                echo "<p style='color: " . ($granted ? 'red' : 'green') . ";'>";
                echo ($granted ? '❌' : '✅') . " الصلاحية في قاعدة البيانات: " . ($granted ? 'مفعلة (خطأ!)' : 'معطلة (صحيح)');
                echo "</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ لم يتم العثور على السجل في قاعدة البيانات</p>";
            }
            $stmt->close();
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في سحب الصلاحية: " . $e->getMessage() . "</p>";
        }
        
        // اختبار جلب صلاحيات المستخدم
        echo "<h3>3. اختبار جلب صلاحيات المستخدم</h3>";
        $user_permissions = $permissions_system->getUserPermissions($test_user_id);
        
        if (isset($user_permissions['purchase_invoices']['create'])) {
            $has_create = $user_permissions['purchase_invoices']['create'];
            echo "<p style='color: " . ($has_create ? 'red' : 'green') . ";'>";
            echo ($has_create ? '❌' : '✅') . " صلاحية الإنشاء في getUserPermissions: " . ($has_create ? 'مفعلة (خطأ!)' : 'معطلة (صحيح)');
            echo "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لم يتم العثور على صلاحية الإنشاء في getUserPermissions</p>";
        }
        
        // عرض جميع صلاحيات المستخدم
        echo "<h3>4. جميع صلاحيات المستخدم</h3>";
        echo "<pre>";
        print_r($user_permissions);
        echo "</pre>";
        
    } else {
        echo "<p style='color: orange;'>⚠️ لم يتم العثور على مستخدم للاختبار</p>";
    }
    
    // فحص جدول user_permissions
    echo "<h2>محتوى جدول user_permissions</h2>";
    $all_permissions_query = "SELECT up.*, m.module_name, p.permission_name, a.username 
                             FROM user_permissions up 
                             JOIN modules m ON up.module_id = m.module_id 
                             JOIN permissions p ON up.permission_id = p.permission_id 
                             JOIN accounts a ON up.account_id = a.account_id 
                             ORDER BY up.created_at DESC LIMIT 10";
    $result = $conn->query($all_permissions_query);
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>المستخدم</th><th>الوحدة</th><th>الصلاحية</th><th>مفعلة</th><th>تاريخ الإنشاء</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $granted_color = $row['granted'] ? 'green' : 'red';
            $granted_text = $row['granted'] ? 'نعم' : 'لا';
            
            echo "<tr>";
            echo "<td>{$row['username']}</td>";
            echo "<td>{$row['module_name']}</td>";
            echo "<td>{$row['permission_name']}</td>";
            echo "<td style='color: $granted_color; font-weight: bold;'>$granted_text</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>لا توجد صلاحيات خاصة في الجدول</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>روابط الاختبار</h2>";
echo "<a href='manage_permissions.php' style='display: inline-block; background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin: 5px;'>إدارة الصلاحيات</a>";
echo "<a href='quick_permissions_test.php' style='display: inline-block; background: #27ae60; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin: 5px;'>اختبار سريع</a>";
echo "<a href='stores.php' style='display: inline-block; background: #f39c12; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; margin: 5px;'>الصفحة الرئيسية</a>";

?>
