<?php
/**
 * اختبار صلاحيات فواتير الشراء
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين الملفات المطلوبة
include_once 'db_connection.php';
include_once 'encryption_functions.php';

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = parse_ini_file(__DIR__ . '/.env');
    foreach ($dotenv as $key => $value) {
        putenv("$key=$value");
    }
}

$key = getenv('ENCRYPTION_KEY');

// محاكاة تسجيل دخول مدير
if (!isset($_SESSION['account_id'])) {
    $admin_query = "SELECT account_id FROM accounts WHERE role = 'admin' AND status = 'active' LIMIT 1";
    $admin_result = $conn->query($admin_query);
    
    if ($admin_result && $admin_result->num_rows > 0) {
        $admin = $admin_result->fetch_assoc();
        $_SESSION['account_id'] = encrypt($admin['account_id'], $key);
        $_SESSION['username'] = 'admin_test';
        $_SESSION['role'] = 'admin';
    }
}

// تضمين نظام الصلاحيات
include_once 'permissions_system.php';
include_once 'auth_check.php';

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صلاحيات فواتير الشراء</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Cairo', sans-serif;
        }
        
        .test-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .permission-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #3498db;
        }
        
        .permission-card.success {
            border-right-color: #27ae60;
            background: #d5f4e6;
        }
        
        .permission-card.error {
            border-right-color: #e74c3c;
            background: #fdeaea;
        }
        
        .module-header {
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .role-section {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .role-header {
            background: #2c3e50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        th, td {
            padding: 10px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        th {
            background: #3498db;
            color: white;
        }
        
        .status-yes {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-no {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-shopping-cart"></i> اختبار صلاحيات فواتير الشراء</h1>
        
        <!-- اختبار الوحدات والصلاحيات الجديدة -->
        <div class="test-section">
            <h2><i class="fas fa-database"></i> الوحدات والصلاحيات الجديدة</h2>
            
            <?php
            // اختبار الوحدات الجديدة
            $purchase_modules = [
                'purchases' => 'فواتير الشراء الرئيسية',
                'purchase_add' => 'إضافة فاتورة شراء',
                'purchase_edit' => 'تعديل فاتورة شراء',
                'purchase_delete' => 'حذف فاتورة شراء',
                'purchase_export' => 'تصدير فواتير الشراء',
                'purchase_reports' => 'تقارير فواتير الشراء',
                'purchase_images' => 'صور فواتير الشراء',
                'purchase_status' => 'حالة فواتير الشراء',
                'purchase_temp' => 'البيانات المؤقتة'
            ];
            
            echo "<div class='module-header'>وحدات فواتير الشراء</div>";
            echo "<div class='permission-grid'>";
            
            foreach ($purchase_modules as $module => $description) {
                $module_exists = $conn->query("SELECT COUNT(*) as count FROM modules WHERE module_name = '$module'")->fetch_assoc()['count'];
                $class = $module_exists > 0 ? 'success' : 'error';
                $icon = $module_exists > 0 ? 'check-circle' : 'times-circle';
                
                echo "<div class='permission-card $class'>";
                echo "<i class='fas fa-$icon'></i> $description";
                echo "<br><small>$module</small>";
                echo "</div>";
            }
            echo "</div>";
            
            // اختبار الصلاحيات الجديدة
            $new_permissions = [
                'confirm_invoice' => 'تأكيد الفاتورة',
                'view_invoice_details' => 'عرض تفاصيل الفاتورة',
                'upload_invoice_images' => 'رفع صور الفاتورة',
                'generate_reports' => 'إنشاء التقارير',
                'search_items' => 'البحث في الأصناف',
                'manage_invoice_status' => 'إدارة حالة الفاتورة',
                'bulk_operations' => 'العمليات المجمعة',
                'access_temp_data' => 'الوصول للبيانات المؤقتة'
            ];
            
            echo "<div class='module-header'>الصلاحيات الجديدة</div>";
            echo "<div class='permission-grid'>";
            
            foreach ($new_permissions as $permission => $description) {
                $permission_exists = $conn->query("SELECT COUNT(*) as count FROM permissions WHERE permission_name = '$permission'")->fetch_assoc()['count'];
                $class = $permission_exists > 0 ? 'success' : 'error';
                $icon = $permission_exists > 0 ? 'check-circle' : 'times-circle';
                
                echo "<div class='permission-card $class'>";
                echo "<i class='fas fa-$icon'></i> $description";
                echo "<br><small>$permission</small>";
                echo "</div>";
            }
            echo "</div>";
            ?>
        </div>
        
        <!-- اختبار صلاحيات الأدوار -->
        <div class="test-section">
            <h2><i class="fas fa-users-cog"></i> اختبار صلاحيات الأدوار</h2>
            
            <?php
            $roles_to_test = [
                1 => 'مدير النظام (admin)',
                2 => 'مدير فرع (store_manager)', 
                3 => 'مشتري (purchaser)',
                4 => 'مستخدم عادي (user)'
            ];
            
            $permissions_to_test = [
                'view' => 'عرض',
                'create' => 'إنشاء',
                'edit' => 'تعديل',
                'delete' => 'حذف',
                'export' => 'تصدير',
                'confirm_invoice' => 'تأكيد الفاتورة',
                'view_invoice_details' => 'عرض التفاصيل',
                'upload_invoice_images' => 'رفع الصور',
                'generate_reports' => 'التقارير',
                'manage_invoice_status' => 'إدارة الحالة'
            ];
            
            foreach ($roles_to_test as $role_id => $role_name) {
                echo "<div class='role-section'>";
                echo "<div class='role-header'>$role_name</div>";
                
                echo "<table>";
                echo "<tr><th>الوحدة</th>";
                foreach ($permissions_to_test as $perm_key => $perm_name) {
                    echo "<th>$perm_name</th>";
                }
                echo "</tr>";
                
                foreach ($purchase_modules as $module => $module_desc) {
                    echo "<tr><td><strong>$module_desc</strong></td>";
                    
                    foreach ($permissions_to_test as $perm_key => $perm_name) {
                        $sql = "SELECT COUNT(*) as count FROM role_permissions rp 
                                JOIN modules m ON rp.module_id = m.module_id 
                                JOIN permissions p ON rp.permission_id = p.permission_id 
                                WHERE rp.role_id = $role_id 
                                AND m.module_name = '$module' 
                                AND p.permission_name = '$perm_key' 
                                AND rp.granted = TRUE";
                        
                        $result = $conn->query($sql);
                        $has_permission = $result->fetch_assoc()['count'] > 0;
                        
                        $status_class = $has_permission ? 'status-yes' : 'status-no';
                        $status_text = $has_permission ? '✓' : '✗';
                        
                        echo "<td class='$status_class'>$status_text</td>";
                    }
                    echo "</tr>";
                }
                
                echo "</table>";
                echo "</div>";
            }
            ?>
        </div>
        
        <!-- اختبار الدوال -->
        <div class="test-section">
            <h2><i class="fas fa-code"></i> اختبار دوال الصلاحيات</h2>
            
            <?php
            try {
                echo "<div class='permission-grid'>";
                
                // اختبار دالة hasAnyPermission
                $test_permissions = [
                    ['purchases', ['view']],
                    ['purchase_add', ['create']],
                    ['purchase_edit', ['edit']],
                    ['purchase_delete', ['delete']],
                    ['purchase_export', ['export']],
                    ['purchase_reports', ['generate_reports']]
                ];
                
                foreach ($test_permissions as $test) {
                    $has_perm = hasAnyPermission($test[0], $test[1]);
                    $class = $has_perm ? 'success' : 'error';
                    $icon = $has_perm ? 'check-circle' : 'times-circle';
                    
                    echo "<div class='permission-card $class'>";
                    echo "<i class='fas fa-$icon'></i> {$test[0]} - {$test[1][0]}";
                    echo "<br><small>النتيجة: " . ($has_perm ? 'مسموح' : 'غير مسموح') . "</small>";
                    echo "</div>";
                }
                
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>خطأ في اختبار الدوال: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <!-- روابط للاختبار -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> روابط الاختبار</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="purchase_invoices.php?store_id=<?php echo urlencode(encrypt(1, $key)); ?>" style="background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-shopping-cart"></i> فواتير الشراء
                </a>
                <a href="add_purchase_invoice.php?store_id=<?php echo urlencode(encrypt(1, $key)); ?>" style="background: #27ae60; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-plus"></i> إضافة فاتورة
                </a>
                <a href="manage_permissions.php" style="background: #e74c3c; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-user-shield"></i> إدارة الصلاحيات
                </a>
                <a href="test_permissions.php" style="background: #f39c12; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none;">
                    <i class="fas fa-vial"></i> اختبار عام
                </a>
            </div>
        </div>
    </div>
</body>
</html>
