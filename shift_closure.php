<?php
include 'db_connection.php';
include 'encryption_functions.php';
include 'cashier_permissions.php';

// فحص صلاحية الوصول لتقفيل الوردية
requireCashierPermission('cashier_shift_closure', 'access', 'ليس لديك صلاحية للوصول لصفحة تقفيل الوردية');

// فحص صلاحية تقفيل الوردية
if (!canCloseShift()) {
    showCashierError('ليس لديك صلاحية لتقفيل الوردية');
    exit();
}

$key = getenv('ENCRYPTION_KEY');
session_start();

$encrypted_account_id = $_GET['account_id'] ?? null;
$account_id = $encrypted_account_id ? decrypt($encrypted_account_id, $key) : null;

if (!$account_id) {
    die("Account ID not found. Please log in again.");
}

// Fetch the store_id using the account_id
$query = "SELECT store_id FROM accounts WHERE account_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $account_id);
$stmt->execute();
$result = $stmt->get_result();
$account = $result->fetch_assoc();
$store_id = $account['store_id'] ?? null;

if (!$store_id) {
    die("Store ID not found for the given Account ID.");
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>تقفيل وردية</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            padding: 20px;
            color: #333;
            transition: background-color 0.3s, color 0.3s;
        }

        body.dark-mode {
            background-color: #1c1e21;
            color: #e4e6eb;
        }

        .form-container {
            max-width: 480px;
            margin: 40px auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            direction: rtl;
            transition: background-color 0.3s, color 0.3s;
        }

        body.dark-mode .form-container {
            background-color: #242526;
            color: #e4e6eb;
        }

        .form-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            margin-bottom: 20px;
        }

        body.dark-mode .form-title {
            color: #007bff;
        }

        .form-label {
            font-weight: 600;
            color: #444;
            margin-bottom: 10px;
            display: inline-block;
        }

        body.dark-mode .form-label {
            color: #e4e6eb;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            margin-bottom: 20px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            color: #333;
            transition: background-color 0.3s, color 0.3s;
        }

        body.dark-mode .form-control {
            background-color: #333;
            color: #e4e6eb;
            border: 1px solid #555;
        }

        .btn-primary {
            display: block;
            width: 100%;
            padding: 14px;
            border-radius: 10px;
            font-weight: bold;
            background-color: #007bff;
            border: none;
            color: #fff;
            text-align: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        body.dark-mode .btn-primary {
            background-color: #0056b3;
            color: #fff;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            transform: scale(1.02);
        }

        body.dark-mode .btn-primary:hover {
            background-color: #003f7f;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="form-title">تقفيل الوردية</div>
        <form id="shift-closure-form" action="save_shift_closure.php" method="POST">
            <input type="hidden" name="account_id" value="<?= htmlspecialchars($encrypted_account_id) ?>">
            <input type="hidden" name="store_id" value="<?= htmlspecialchars(encrypt($store_id, $key)) ?>">

            <label for="shift_date" class="form-label">تاريخ اليوم</label>
            <input type="date" class="form-control" id="shift_date" name="shift_date" value="<?= date('Y-m-d') ?>" required>

            <label for="shift_type" class="form-label">نشاط الوردية</label>
            <select class="form-control" id="shift_type" name="shift_type" required>
                <option value="morning">صباحية</option>
                <option value="night">مسائية</option>
            </select>

            <label for="shift_amount" class="form-label">نقدي الوردية</label>
            <input type="number" class="form-control" id="shift_amount" name="shift_amount" step="0.01" required>

            <label for="purchases" class="form-label">المشتريات</label>
            <input type="number" class="form-control" id="purchases" name="purchases" step="0.01" required>

            <label for="notes" class="form-label">ملاحظات</label>
            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>

            <button type="submit" class="btn-primary">تسجيل</button>
        </form>
    </div>
    <div style="margin-bottom: 80px;"></div>
    <?php include 'bottom_nav.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.getElementById('shift-closure-form').addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = new FormData(this);

            fetch('save_shift_closure.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم تسجيل الوردية بنجاح',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message,
                        showConfirmButton: true
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ أثناء إرسال الطلب.',
                    showConfirmButton: true
                });
            });
        });

        // Apply theme based on localStorage
        const body = document.body;

        if (localStorage.getItem('dark-mode') === 'true') {
            body.classList.add('dark-mode');
        }
    </script>
</body>
</html>
