<?php
require_once 'auth.php';

include 'db_connection.php';
include 'encryption_functions.php';
include 'check_expiry_notifications.php';
include 'cashier_permissions.php';

// فحص صلاحية الوصول لنظام الكاشير
requireCashierPermission('cashier_home', 'access', 'ليس لديك صلاحية للوصول للصفحة الرئيسية لنظام الكاشير');

$key = getenv('ENCRYPTION_KEY');
session_start();

if (isset($_GET['account_id'])) {
    $encrypted_account_id = $_GET['account_id'];
    $account_id = decrypt($encrypted_account_id, $key);

    if ($account_id === false) {
        die("Failed to decrypt Account ID.");
    }

    // Fetch the store_id using the account_id
    $query = "SELECT store_id FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $account = $result->fetch_assoc();
    $store_id = $account['store_id'] ?? null;

    if (!$store_id) {
        die("Store ID not found for the given Account ID.");
    }

    // Encrypt the store_id for session storage
    $encrypted_store_id = encrypt($store_id, $key);

    // Store the encrypted store ID and account ID in the session
    $_SESSION['store_id'] = $encrypted_store_id;
    $_SESSION['account_id'] = $encrypted_account_id;
    setcookie('account_id', $encrypted_account_id, time() + (86400 * 30), "/");

    // Fetch items related to the store with active status
    $query = "SELECT items.name, items.price FROM items
              JOIN categories ON items.category_id = categories.category_id
              WHERE categories.store_id = ? AND items.status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();

    // Store the initial items in a variable
    $initialItems = [];
    while ($row = $result->fetch_assoc()) {
        $initialItems[] = $row;
    }

    // Fetch the user's first name using the account ID
    $query = "SELECT name FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $firstName = explode(' ', $user['name'])[0];

    // Fetch the store name using the store ID
    $query = "SELECT name FROM stores WHERE store_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $store = $result->fetch_assoc();
    $storeName = $store['name'] ?? 'اسم الفرع غير متوفر';

    // Fetch the user's theme preference
    $query = "SELECT theme FROM accounts WHERE account_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $userTheme = $user['theme'] ?? 'Light';

    // Set the theme in localStorage based on the database value
    echo "<script>
        localStorage.setItem('dark-mode', " . ($userTheme === 'Dark' ? 'true' : 'false') . ");
    </script>";
} else {
    die("Account ID not provided.");
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <title>ELWALED MARKET</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <!-- Include original CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Add Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" type="text/css" href="web_css/style_index.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <!-- Add Arabic font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <!-- Include Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Include FontAwesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Include SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            transition: background-color 0.3s, color 0.3s;
            background: url('https://source.unsplash.com/1600x900/?ramadan') no-repeat center center/cover;
            text-align: center;
            color: gold;
            overflow-y: auto; /* Ensure the body can scroll */
        }

        .dark-mode {
            background-color: #1c1e21;
            color: #e4e6eb;
        }

        .container {
            margin-top: 20px;
        }

        h2 {
            font-family: 'Cairo';
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            color: #007bff;
        }

        .store-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }

        .store-card {
            font-family: 'Cairo', Arial, sans-serif;
            width: 250px;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease, background-color 0.3s ease;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .store-card:hover {
            transform: scale(1.05);
        }

        .dark-mode .store-card {
            background-color: #242526;
        }

        .store-card img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .store-card h3 {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 0;
            font-size: 20px;
            font-weight: bold;
            color: #007bff; /* Match the site title color */
        }

        .store-card.no-image h3 {
            font-size: 2em;
            margin: auto;
        }

        @media (max-width: 800px) {
            .store-card {
                width: 200px;
                padding: 15px;
            }

            .store-card h3 {
                font-size: 18px;
            }

            .store-card.no-image h3 {
                font-size: 1.5em;
            }
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 20px;
            background-color: #007bff;
            color: #ffffff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: background-color 0.3s, color 0.3s;
        }

        .dark-mode .header {
            background-color: #242526;
            color: #e4e6eb;
        }

        .store-name {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 1.5em;
            font-weight: bold;
            color: #ffffff;
            text-align: center;
        }

        .toggle-switch {
            display: flex;
            align-items: center;
        }

        .toggle-switch input {
            display: none;
        }

        .toggle-switch label {
            cursor: pointer;
            width: 50px;
            height: 25px;
            background-color: #ccc;
            border-radius: 25px;
            position: relative;
            transition: background-color 0.3s;
        }

        .toggle-switch label::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background-color: #ffffff;
            border-radius: 50%;
            transition: transform 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toggle-switch label .icon {
            display: none;
        }

        .toggle-switch input:checked + label {
            background-color: #4caf50;
        }

        .toggle-switch input:checked + label::after {
            transform: translateX(25px);
        }

        .btn-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 25px;
            background-color: #ccc;
            border-radius: 25px;
            position: relative;
            transition: background-color 0.3s;
            cursor: pointer;
        }

        .btn-toggle .light-icon,
        .btn-toggle .dark-icon {
            position: absolute;
            transition: opacity 0.3s;
        }

        .btn-toggle .light-icon {
            opacity: 1;
        }

        .btn-toggle .dark-icon {
            opacity: 0;
        }

        .dark-mode .btn-toggle .light-icon {
            opacity: 0;
        }

        .dark-mode .btn-toggle .dark-icon {
            opacity: 1;
        }

        .preloader-container {
            position: fixed;
            top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100vh;
            background: #f0f2f5; /* Match the internal background color */
            z-index: 9999999;
            flex-direction: column;
        }

        .dark-mode .preloader-container {
            background: #1c1e21; /* Match the internal background color */
        }

        .preloader-container.active_new {
            transform: translateY(-100vh);
            transition: ease-in-out 0.5s;
            transition-delay: 0.3s;
        }

        .preloader-text {
            top: 52%;
            font-size: 66px;
            height: 500px;
            position: absolute;
            width: 100%;
            margin: auto;
            text-align: center;
            color: #007bff;
        }

        .dark-mode .preloader-text {
            color: #e4e6eb;
        }

        .preloader-container.active_new .preloader-text {
            display: none;
        }

        .preloader-logo {
            width: 150px;
            height: 150px; /* Increase the height */
            margin-bottom: 20px;
            animation: zoomInOut 2s infinite;
        }

        @keyframes zoomInOut {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
            }
        }

        .search-bar {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: #f0f2f5;
            padding: 10px 0;
        }

        .dark-mode .search-bar {
            background-color: #1c1e21;
        }

        .search-bar input {
            width: 50%;
            border: none;
            outline: none;
            padding: 10px;
            font-size: 16px;
            border-radius: 25px;
            color: #007bff; /* Match the site title color */
        }

        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            padding: 12px 15px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .table th {
            background-color: #007bff;
            color: #ffffff;
        }

        .dark-mode .table th {
            background-color: #555555;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .dark-mode .table tbody tr:hover {
            background-color: #666666;
        }

        .table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .dark-mode .table tr:nth-child(even) {
            background-color: #444444;
        }

        .table tr:hover {
            background-color: #ddd;
        }

        .dark-mode .table tr:hover {
            background-color: #666666;
        }

        .no-results {
            text-align: center;
            font-size: 1.2em;
            color: #ff0000;
            margin-top: 20px;
        }

        .scroll-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #007bff;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s;
            opacity: 0;
            visibility: hidden;
        }

        .scroll-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .dark-mode .scroll-to-top {
            background-color: #555555;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: -1;
            pointer-events: none;
        }

        .star {
            position: absolute;
            width: 4px;
            height: 4px;
            background: gold;
            border-radius: 50%;
            animation: twinkle 1.5s infinite ease-in-out, move 10s linear infinite;
        }

        .dark-mode .star {
            background: white;
        }

        @keyframes twinkle {
            0%, 100% {
                opacity: 0.5;
            }
            50% {
                opacity: 1;
            }
        }

        @keyframes move {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-100vh);
            }
        }
        .add-invoice-icon {
            position: absolute;
            right: 20px;
            top: 15px;
            font-size: 1.5rem;
            color: white;
            text-decoration: none;
            background-color: #007bff;
            padding: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s, transform 0.3s;
        }

        .add-invoice-icon:hover {
            background-color: #0056b3;
            transform: scale(1.1);
        }

        /* Bottom Navigation Bar Styles */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background-color: #fff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 9999;
        }

        .bottom-nav a {
            text-decoration: none;
            color: #333;
            font-size: 0.9rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 5px;
        }

        .bottom-nav a.active {
            color: #007bff;
            font-weight: bold;
        }

        .bottom-nav a i {
            font-size: 22px;
            margin-bottom: 2px;
        }

        /* Dark Mode Styles */
        .dark-mode .bottom-nav {
            background-color: #242526;
        }

        .dark-mode .bottom-nav a {
            color: #e4e6eb;
        }

        .dark-mode .bottom-nav a.active {
            color: #4caf50;
        }
    </style>
</head>
<body>
    <div id="stars"></div>
    <header class="header d-flex justify-content-between align-items-center">
        <div class="store-name">ELWALED MARKET</div>
        <button id="theme-toggle" class="btn btn-toggle">
            <span class="light-icon"><i class="fas fa-moon"></i></span>
            <span class="dark-icon"><i class="fas fa-sun"></i></span>
        </button>
    </header>
    <div class="search-bar">
        <input type="text" id="search-input" placeholder="ابحث عن صنف...">
    </div>

    <div class="container mx-auto mt-5">
        <h2 class="text-3xl font-bold text-center text-blue-600 mb-5">قائمة المنتجات</h2>
        <h3 class="text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-5" style="font-family: 'Cairo', Arial, sans-serif;">
            الفرع: <?= htmlspecialchars($storeName) ?>
        </h3>
        <div class="overflow-x-auto">
            <table class="table-auto w-full bg-white shadow-lg rounded-lg border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
                <thead class="bg-blue-500 text-white">
                    <tr>
                        <th class="px-6 py-3 text-right text-lg font-semibold">اسم المنتج</th>
                        <th class="px-6 py-3 text-right text-lg font-semibold">السعر</th>
                    </tr>
                </thead>
                <tbody id="items-table-body">
                    <?php if (!empty($initialItems)): ?>
                        <?php foreach ($initialItems as $item): ?>
                            <tr class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 transition duration-300">
                                <td class="px-6 py-4 text-right text-gray-800 dark:text-gray-200">
                                    <i class="fas fa-box-open text-blue-500 mr-2"></i> <?= htmlspecialchars($item['name']) ?>
                                </td>
                                <td class="px-6 py-4 text-right text-gray-800 dark:text-gray-200">
                                    <i class="fas fa-dollar-sign text-green-500 mr-2"></i> <?= number_format($item['price'], 2) ?> جنيه
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="2" class="text-center text-red-500">لا توجد أصناف متاحة.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            <div id="no-results" class="no-results" style="display: none;">لا توجد نتائج مطابقة.</div>
        </div>
    </div>
    <div style="margin-bottom: 80px;"></div>
    <div style="margin-bottom: 80px;"></div>

    <button id="scroll-to-top" class="scroll-to-top"><i class="fas fa-arrow-up"></i></button>

    <script src="js/jquery-1.11.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script type="text/javascript" src="js/script.js"></script>
    <script>
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 20; i++) {
                const star = document.createElement('div');
                star.classList.add('star');
                star.style.top = `${Math.random() * 100}vh`;
                star.style.left = `${Math.random() * 100}vw`;
                star.style.animationDuration = `${Math.random() * 2 + 1}s, ${Math.random() * 20 + 10}s`;
                starsContainer.appendChild(star);
            }
        }

        document.addEventListener('DOMContentLoaded', createStars);

        const themeToggle = document.getElementById('theme-toggle');
        const body = document.body;

        // Apply theme based on localStorage
        if (localStorage.getItem('dark-mode') === 'true') {
            body.classList.add('dark-mode');
        } else {
            body.classList.remove('dark-mode');
        }

        themeToggle.addEventListener('click', () => {
            const isDarkMode = body.classList.toggle('dark-mode');
            localStorage.setItem('dark-mode', isDarkMode);

            // Update the theme in the database
            fetch('update_theme.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    account_id: <?= json_encode($account_id) ?>,
                    theme: isDarkMode ? 'Dark' : 'Light'
                })
            }).then(response => response.json())
              .then(data => {
                  if (!data.success) {
                      console.error('Failed to update theme:', data.message);
                  }
              }).catch(error => {
                  console.error('Error updating theme:', error);
              });
        });

        // Live search functionality
        const searchInput = document.getElementById('search-input');
        const itemsTableBody = document.getElementById('items-table-body');
        const noResults = document.getElementById('no-results');
        const initialItems = <?php echo json_encode($initialItems); ?>;
        const storeId = <?php echo json_encode($encrypted_store_id); ?>;

        searchInput.addEventListener('input', function() {
            const query = this.value;
            if (query.length > 0) {
                fetch(`search_items.php?q=${query}&store_id=${storeId}`)
                    .then(response => response.json())
                    .then(data => {
                        itemsTableBody.innerHTML = '';
                        if (data.length > 0) {
                            data.forEach(item => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td class="px-6 py-4 text-right text-gray-800 dark:text-gray-200">
                                        <i class="fas fa-box-open text-blue-500 mr-2"></i> ${item.name}
                                    </td>
                                    <td class="px-6 py-4 text-right text-gray-800 dark:text-gray-200">
                                        <i class="fas fa-dollar-sign text-green-500 mr-2"></i> ${item.price} جنيه
                                    </td>
                                `;
                                itemsTableBody.appendChild(row);
                            });
                            noResults.style.display = 'none';
                        } else {
                            noResults.style.display = 'block';
                        }
                    });
            } else {
                itemsTableBody.innerHTML = '';
                initialItems.forEach(item => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 text-right text-gray-800 dark:text-gray-200">
                            <i class="fas fa-box-open text-blue-500 mr-2"></i> ${item.name}
                        </td>
                        <td class="px-6 py-4 text-right text-gray-800 dark:text-gray-200">
                            <i class="fas fa-dollar-sign text-green-500 mr-2"></i> ${item.price} جنيه
                        </td>
                    `;
                    itemsTableBody.appendChild(row);
                });
                noResults.style.display = 'none';
            }
        });

        // Scroll-to-top functionality
        const scrollToTopButton = document.getElementById('scroll-to-top');

        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                scrollToTopButton.classList.add('show');
            } else {
                scrollToTopButton.classList.remove('show');
            }
        });

        scrollToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>

</body>

<!-- Include Bottom Navigation -->
<?php include 'bottom_nav.php'; ?>

</html>